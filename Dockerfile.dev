# syntax=docker/dockerfile:1
FROM hub-docker-remote.artylab.expedia.biz/golang:1.24 AS builder

ARG GITHUB_ACCESS_TOKEN

WORKDIR /workspace

COPY ./go.mod ./go.sum ./
RUN git config --global url."https://${GITHUB_ACCESS_TOKEN}:@github.expedia.biz/".insteadOf "https://github.expedia.biz/" && \
    go mod download && \
    git config --global --unset url."https://${GITHUB_ACCESS_TOKEN}:@github.expedia.biz/".insteadOf

COPY . .
RUN ./scripts/build.sh
RUN ./scripts/server.sh

FROM hub-docker-remote.artylab.expedia.biz/debian:bookworm
WORKDIR /workspace

RUN apt-get update \
 && apt-get install -y --no-install-recommends \
      ca-certificates awscli \
 && rm -rf /var/lib/apt/lists/*


# EG local zscalar certs
# you need to download the ZscalerRootCA.pem and store in the loki repo
# follow this instructions:https://expediagroup.atlassian.net/wiki/spaces/~akostyuchenko/pages/*********/Zscaler+cert+fix+for+Docker
COPY ZscalerRootCA.pem /usr/local/share/ca-certificates/

COPY --from=builder /workspace/bin /bin
COPY --from=builder /workspace/resources /workspace/resources

EXPOSE 50051
EXPOSE 8080
EXPOSE 9090
