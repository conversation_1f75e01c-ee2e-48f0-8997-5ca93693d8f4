---
name: GHAS-CodeQL-Scan
on:
  workflow_dispatch:
  pull_request:
    branches:
      - main
  schedule:
    - cron: '42 3 * * 2'
  push:
    branches:
      - main
jobs:
  analyze:
    name: Analyze
    runs-on:
      - eg-securityscan
    strategy:
      fail-fast: false
      matrix:
        language:
          - go
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
      - name: Initialize CodeQL
        uses: github/codeql-action/init@v3
        with:
          languages: ${{ matrix.language }}
          queries: security-extended
      - name: Setup Go
        uses: actions/setup-go@v4
        with:
          go-version: '~1.23.0'
      - name: Autobuild
        # Autobuild attempts to build compiled languages (C/C++, C#, or Java)
        # 📝️ If the Autobuild step fails, remove the step entirely, uncomment
        # the 'Custom Build' step below, and modify to build your code.
        if: |
          matrix.language == 'c' ||
          matrix.language == 'cpp' ||
          matrix.language == 'csharp' ||
          matrix.language == 'go' ||
          matrix.language == 'java'
        uses: github/codeql-action/autobuild@v3
      # - name: Custom Build
      # ℹ️ The step name 'Custom Build' is required in order to prevent the
      # Autobuild step from being re-added during future workflow updates.
      #   run: |
      #     make bootstrap
      #     make release
      - name: Perform CodeQL Analysis
        uses: github/codeql-action/analyze@v3
