name: <PERSON> Handler
# Warning: The pull_request_target event is granted a read/write repository token and can access secrets, even when it is triggered from a fork.
on: pull_request_target

env:
  S_EG_SEM_ENG_EGGH_PAT: ${{ secrets.S_EG_SEM_ENG_EGGH_PAT }}
  GOPRIVATE: "github.expedia.biz/*"
  GIT_ASKPASS: ${{ github.workspace }}/askpass.sh

jobs:
  handle-pr:
    runs-on: eg-default
    env:
      IMAGE_TAG: ${{ github.sha }}
    steps:
      - name: Checkout
        uses: actions/checkout@v2
        with:
          fetch-depth: 0
          # Due to using `pull_request_target`, we have to explicitly set the ref, otherwise it'll use the main ref
          # https://github.com/actions/checkout/issues/518
          ref: "refs/pull/${{ github.event.number }}/merge"

      - name: Setup Go
        uses: actions/setup-go@v4
        with:
          go-version: '~1.24.0'

      - name: Setup GIT_ASKPASS script
        run: |
          echo "Setting up GIT_ASKPASS..."

          # Create script that that echos the token
          echo 'echo "$S_EG_SEM_ENG_EGGH_PAT";' > "$GIT_ASKPASS"
          chmod u+x "$GITHUB_WORKSPACE/askpass.sh"

      - name: build
        run: |
          make build

      - name: Run tests and verify build
        run: |
          make test
