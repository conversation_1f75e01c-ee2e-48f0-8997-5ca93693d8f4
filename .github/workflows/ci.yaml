name: CI
on:
  push:
    branches:
    - main
    paths-ignore:
    - "**.md"
env:
  S_EG_SEM_ENG_EGGH_PAT: ${{ secrets.S_EG_SEM_ENG_EGGH_PAT }}
  GOPRIVATE: "github.expedia.biz/*"
  GIT_ASKPASS: ${{ github.workspace }}/askpass.sh

jobs:
  build:
    runs-on: eg-default
    env:
      IMAGE_TAG: ${{ github.sha }}
      RELEASE_REGISTRY: eg-docker-release-local.artylab.expedia.biz
    steps:
      - name: Checkout
        uses: actions/checkout@v2
        with:
          token: ${{ secrets.S_EG_SEM_ENG_EGGH_PAT }}
     
      - name: Set up Docker Buildx
        uses: actions/docker-setup-buildx-action@v3
     
      - name: Docker Login to docker release registry
        uses: actions/docker-login-action@v3
        with:
          registry: ${{ env.RELEASE_REGISTRY }}
          username: ${{ secrets.EG_ARTY_USER }}
          password: ${{ secrets.EG_ARTY_PASSWORD }}
      
      - name: Docker Registry Login - hub-docker-remote
        uses: actions/docker-login-action@v3
        with:
          registry: hub-docker-remote.artylab.expedia.biz
          username: ${{ secrets.EG_ARTY_USER }}
          password: ${{ secrets.EG_ARTY_PASSWORD }}

      - name: Docker Build & Publish
        uses: actions/docker-build-push-action@v5
        with:
          context: .
          file: 'Dockerfile'
          platforms: linux/amd64
          build-args: |
            TAG=${{ github.sha }}
            GITHUB_ACCESS_TOKEN=${{ secrets.S_EG_SEM_ENG_EGGH_PAT }}
          push: true
          tags: |
            ${{ env.RELEASE_REGISTRY }}/marketing/${{ github.event.repository.name }}:${{ github.sha }}
          labels: |
            ${{ github.event.repository.name }}:${{ github.sha }}

  deploy-test:
    runs-on: eg-default
    needs: build
    steps:
      - name: Deploy
        uses: gmo-performance-marketing/gitops-deploy-action@main
        with:
          gitops-repo: 'gmo-performance-marketing/applications'
          git-pat: ${{ secrets.S_EG_SEM_ENG_EGGH_PAT }}
          application-name: loki/backend
          image-tag: ${{ github.sha }}
          environment: 'test'

  deploy-prod:
    runs-on: eg-default
    environment: 'prod'
    needs: deploy-test
    steps:
      - name: Deploy
        uses: gmo-performance-marketing/gitops-deploy-action@main
        with:
          gitops-repo: 'gmo-performance-marketing/applications'
          git-pat: ${{ secrets.S_EG_SEM_ENG_EGGH_PAT }}
          application-name: loki/backend
          image-tag: ${{ github.sha }}
          environment: 'prod'
