# Protos

Loki service protos are found in the repo https://github.expedia.biz/gmo-performance-marketing/protos/tree/main/rpc/loki/v1

# Overview
### Models Service (model_svc.proto)
This service manages the lifecycle of machine learning models and their executions.

### Core Messages:

* **Model**: Represents a machine learning model with configuration for execution
* **ModelRun**: Represents an instance of a model execution

### Service Contract Notes:
* Models have unique IDs, names, and versions
* Models are associated with specific marketing channels
* Models can be enabled or disabled
* Models can be executed with specific parameters
* Models produce outputs at specific bid units
* Models are executed via orchestrators like Argo or Flyte

[Service Implementation](../internal/model/grpc.go) 

### Rule Service (rule_svc.proto)
This service manages the lifecycle of rule schemas, rules, and rule items.

### Core Messages:

* **RuleSchema**: A template defining the structure of rules
* **Rule**: A collection of rule items based on a schema
* **RuleItem**: A specific instance of a rule with versioning

### Service Contract Notes:

* Rules are versioned for tracking changes over time
* Rules can be attached to either Models or Assemblers
* Rules are associated with specific marketing channels
* Rules are based on schemas that define their structure via JSON schema
* Rule items contain the actual rule logic as JSON that conforms to the schema

[Service Implementation](../internal/rule/grpc.go)

### Bid Unit Service (bid_unit_svc.proto)
This service manages bid units and their configurations.

### Core Messages:

* **BidUnit**: Represents the granularity at which models produce bids
* **BidUnitConfig**: Configuration for a bid unit, including model and rule associations

### Service Contract Notes:

* Bid units are associated with specific marketing channels
* Bid unit configs can be of different types (BAU, Control, Challenger, etc.)
* Bid unit configs can be associated with experiments
* Bid unit configs link models and rules together

[Service Implementation](../internal/bidunit/grpc.go)

### Pipeline Service (pipeline_svc.proto)
This service manages pipelines that orchestrate the execution of models, assemblers, and publishing.

### Core Messages:

* **Pipeline**: A workflow that coordinates model execution, assembly, and publishing
* **PipelineRun**: An instance of a pipeline execution

### Service Contract Notes:

* Pipelines are scheduled using cron expressions
* Pipelines can be configured for auto-publishing based on criteria
* Pipelines coordinate multiple models, an assembler, and post-publish steps
* Pipelines track the status of each step in the process
* Pipelines can be configured with memoization to avoid redundant computations

[Service Implementation](../internal/pipeline/grpc.go)

### Assembler Service (assembler_svc.proto)
This service manages assembler executions that combine model outputs with rules.

### Core Messages:

* **AssemblerRun**: An instance of an assembler execution
* **AssemblerRunOutput**: The output of an assembler run for a specific bid unit config

### Service Contract Notes:

* Assemblers apply rules to model outputs
* Assemblers track success and error counts for bids
* Assemblers can filter inputs based on property inventory
* Assemblers can enrich data with additional information
* Assemblers can perform QA validation on bids

[Service Implementation](../internal/assembler/service/grpc.go)

### Orchestrator (orchestrator.proto)
This component provides configuration and execution context for workflow orchestration systems.

### Core Messages:

* **OrchestratorConfig**: Configuration for a workflow orchestrator
* **OrchestratorExecContext**: Execution context for a workflow

### Service Contract Notes:

* Supports multiple orchestration systems (Argo, Flyte)
* Tracks execution status, start/end times, and errors
* Provides tracking URLs for monitoring executions

[Service Implementation](../internal/pipeline/grpc.go) 
