# syntax=docker/dockerfile:1
FROM hub-docker-remote.artylab.expedia.biz/golang:1.24 AS builder

ARG GITHUB_ACCESS_TOKEN

WORKDIR /workspace

COPY ./go.mod ./go.sum ./
RUN git config --global url."https://${GITHUB_ACCESS_TOKEN}:@github.expedia.biz/".insteadOf "https://github.expedia.biz/" && \
    go mod download && \
    git config --global --unset url."https://${GITHUB_ACCESS_TOKEN}:@github.expedia.biz/".insteadOf

COPY . .
RUN ./scripts/build.sh
RUN ./scripts/server.sh


# Result container:
FROM hub-docker-remote.artylab.expedia.biz/debian:bookworm

WORKDIR /workspace
ARG TAG
ENV VERSION=$TAG

RUN apt-get update \
 && DEBIAN_FRONTEND=noninteractive \
    apt-get install --no-install-recommends --assume-yes \
      ca-certificates awscli

# EG internal server certificates
COPY certs/*.crt /usr/local/share/ca-certificates/
RUN update-ca-certificates

COPY --from=builder /workspace/bin /bin
COPY --from=builder /workspace/resources /workspace/resources

EXPOSE 50051
EXPOSE 8080
EXPOSE 9090
