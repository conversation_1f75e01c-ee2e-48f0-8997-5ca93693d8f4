package service

import (
	"fmt"
	"strconv"
	"time"

	pb "github.expedia.biz/gmo-performance-marketing/pb-go/pkg/loki/v1"
	"github.expedia.biz/gmo-performance-marketing/sem-lib-go/pkg/protoserde"
	"google.golang.org/protobuf/proto"
)

type AssemblerRun struct {
	Id uint64 `gorm:"primaryKey"`

	PipelineRunId           string `gorm:"index:idx_loki_assembler_run_pipeline_run_id"`
	RunParameters           string
	Outputs                 []byte
	RuleItems               []byte
	OrchestratorExecContext []byte
	MarketingEntitiesS3Path string

	StartedAt  time.Time `gorm:"autoCreateTime"`
	UpdatedAt  time.Time `gorm:"autoUpdateTime"`
	FinishedAt time.Time
}

func (AssemblerRun) TableName() string {
	return "loki.assembler_runs"
}

func (a *AssemblerRun) toPb() (*pb.AssemblerRun, error) {
	outputs := make([]*pb.AssemblerRunOutput, 0)
	err := protoserde.UnmarshalArray(a.Outputs, &outputs)
	if err != nil {
		return nil, fmt.Errorf("unmarshalAssemblerOutputs: %w", err)
	}
	ruleItems := make([]*pb.AssemblerRuleItem, 0)
	err = protoserde.UnmarshalArray(a.RuleItems, &ruleItems)
	if err != nil {
		return nil, fmt.Errorf("unmarshalAssemblerRuleItems: %w", err)
	}

	orchestratorExecContext := &pb.OrchestratorExecContext{}
	err = proto.Unmarshal(a.OrchestratorExecContext, orchestratorExecContext)
	if err != nil {
		return nil, fmt.Errorf("unmarshallOrchestratorExecContext: %w", err)
	}
	orchestratorExecContext.StartedAt = protoserde.ToPbTimestamp(a.StartedAt)
	orchestratorExecContext.FinishedAt = protoserde.ToPbTimestamp(a.FinishedAt)

	return &pb.AssemblerRun{
		Id:                      strconv.FormatUint(a.Id, 10),
		RunParameters:           a.RunParameters,
		PipelineRunId:           a.PipelineRunId,
		Outputs:                 outputs,
		RuleItems:               ruleItems,
		OrchestratorExecContext: orchestratorExecContext,
		MarketingEntitiesS3Path: a.MarketingEntitiesS3Path,
	}, nil
}
