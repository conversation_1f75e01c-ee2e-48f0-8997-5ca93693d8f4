package service

import (
	"context"
	"strconv"
	"time"

	pb "github.expedia.biz/gmo-performance-marketing/pb-go/pkg/loki/v1"
	"github.expedia.biz/gmo-performance-marketing/sem-lib-go/pkg/protoserde"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/proto"
)

func (s *service) CreateAssemblerRun(
	ctx context.Context, req *pb.CreateAssemblerRunRequest,
) (*pb.CreateAssemblerRunResponse, error) {
	if req.PipelineRunId == "" {
		return nil, status.Error(codes.InvalidArgument, "`pipeline_run_id` is a required field")
	}

	orchestartorExecContext, err := proto.Marshal(&pb.OrchestratorExecContext{
		ExecutionStatus: pb.OrchestratorExecutionStatus_ORCHESTRATOR_EXECUTION_STATUS_QUEUED,
	})
	if err != nil {
		return nil, status.Errorf(codes.Internal, "marhsalOrchestratorExecContext: %v", err)
	}
	ar := &AssemblerRun{
		PipelineRunId:           req.PipelineRunId,
		RunParameters:           req.RunParameters,
		OrchestratorExecContext: orchestartorExecContext,
	}

	err = s.orm.Create(ar).Error
	if err != nil {
		return nil, status.Errorf(codes.Internal, "createInDB: %v", err)
	}

	return &pb.CreateAssemblerRunResponse{Id: strconv.FormatUint(ar.Id, 10)}, nil
}

func (s *service) GetAssemblerRuns(
	_ context.Context, req *pb.GetAssemblerRunsRequest,
) (*pb.GetAssemblerRunsResponse, error) {
	var assemblerRuns []*AssemblerRun
	query := s.orm

	if req.Id != "" {
		id, err := strconv.ParseUint(req.Id, 10, 64)
		if err != nil {
			return nil, status.Errorf(codes.InvalidArgument, "id: %v is invalid", req.Id)
		}
		query = query.Where("id = ?", id)
	}

	if req.StartedAfter != nil {
		query = query.Where("created_at >= ?", req.StartedAfter.AsTime())
	}
	if req.StartedBefore != nil {
		query = query.Where("created_at <= ?", req.StartedBefore.AsTime())
	}

	err := query.Find(&assemblerRuns).Error // TODO: limit & offset
	if err != nil {
		return nil, status.Errorf(codes.Internal, "getFromDB: %v", err)
	}

	pbAssemblerRuns := make([]*pb.AssemblerRun, len(assemblerRuns))
	for i, assemblerRun := range assemblerRuns {
		prAssemblerRun, err := assemblerRun.toPb()
		if err != nil {
			return nil, status.Errorf(codes.Internal, "toPb: %v", err)
		}
		pbAssemblerRuns[i] = prAssemblerRun
	}

	return &pb.GetAssemblerRunsResponse{Runs: pbAssemblerRuns}, nil
}

func (s *service) UpdateAssemblerRun(
	ctx context.Context, req *pb.UpdateAssemblerRunRequest,
) (*pb.UpdateAssemblerRunResponse, error) {
	if req.Id == "" {
		return nil, status.Error(codes.InvalidArgument, "`id` is a required field")
	}

	id, err := strconv.ParseUint(req.Id, 10, 64)
	if err != nil {
		return nil, status.Errorf(codes.InvalidArgument, "id: %v is invalid", req.Id)
	}

	assemblerRun := &AssemblerRun{
		Id:                      id,
		MarketingEntitiesS3Path: req.MarketingEntitiesS3Path,
	}
	if len(req.Outputs) > 0 {
		assemblerRun.Outputs, err = protoserde.MarshalArray(req.Outputs)
		if err != nil {
			return nil, status.Errorf(codes.Internal, "marshalOutputs: %v", err)
		}
	}
	if len(req.RuleItems) > 0 {
		assemblerRun.RuleItems, err = protoserde.MarshalArray(req.RuleItems)
		if err != nil {
			return nil, status.Errorf(codes.Internal, "marshalRuleItems: %v", err)
		}
	}
	if req.OrchestratorExecContext != nil {
		assemblerRun.OrchestratorExecContext, err = proto.Marshal(req.OrchestratorExecContext)
		if err != nil {
			return nil, status.Errorf(codes.Internal, "marhsalOrchestratorExecContext: %v", err)
		}
	}
	if req.OrchestratorExecContext != nil && req.OrchestratorExecContext.ExecutionStatus == pb.OrchestratorExecutionStatus_ORCHESTRATOR_EXECUTION_STATUS_FINISHED {
		assemblerRun.FinishedAt = time.Now()
	}

	err = s.orm.Updates(&assemblerRun).Error
	if err != nil {
		return nil, status.Errorf(codes.Internal, "updateInDB: %v", err)
	}

	return &pb.UpdateAssemblerRunResponse{}, nil
}
