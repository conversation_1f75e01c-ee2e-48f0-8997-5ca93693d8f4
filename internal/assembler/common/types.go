package common

import (
	"encoding/json"
	"fmt"
	"sort"

	pb "github.expedia.biz/gmo-performance-marketing/pb-go/pkg/loki/v1"
)

type RuleItem struct {
	Id      string
	Info    string
	Version string
}
type AssemblerRule struct {
	RuleSchema string
	RuleId     string
	RuleName   string

	RuleItems []RuleItem
}

type ExperimentConfig struct {
	ExperimentId   string
	ExperimentName string
	BinId          string
}

type BidUnitConfigType int64

const (
	BAU BidUnitConfigType = iota
	CONTROL
	CHALLENGER
	OBSERVE
)

const AssemblerS3Path = "%s/assembler/runId=%s"

type BidUnitConfig struct {
	ExperimentConfig

	Id            string
	Rules         []AssemblerRule
	Type          BidUnitConfigType
	BidUnitId     string
	BidUnitName   string
	AssemberRunId string
	ModelId       string
	ModelName     string
	ModelVersion  string
	EstimatesPath string

	// metadata
	IsError             bool
	Error               string
	LogsPath            string
	AssembledBidsPath   string
	FinalBidsPath       string
	SuccessBids         int64
	ErrorBids           int64
	PartialFailuresPath string
}

func GetBidUnitConfigType(configType pb.BidUnitConfigType) BidUnitConfigType {
	switch configType {
	case pb.BidUnitConfigType_BID_UNIT_CONFIG_TYPE_BAU:
		return BAU
	case pb.BidUnitConfigType_BID_UNIT_CONFIG_TYPE_CHALLENGER:
		return CHALLENGER
	case pb.BidUnitConfigType_BID_UNIT_CONFIG_TYPE_CONTROL:
		return CONTROL
	case pb.BidUnitConfigType_BID_UNIT_CONFIG_TYPE_OBSERVE:
		return OBSERVE
	default:
		return BAU
	}
}

type Estimate struct {
	Input        map[string]interface{}
	EnrichedData map[string]interface{}

	// Tagging the other fields
	IsError      bool    `header:"is_error"`
	Error        string  `header:"error"`
	InputBid     float64 `header:"input_bid"`
	OutputBid    float64 `header:"output_bid"`
	ChangeReason string  `header:"change_reason"`
	IsSkipped    bool    `header:"is_skipped"`
}

// Function to generate the header dynamically using struct tags
func (e *Estimate) GetHeaders() []string {
	// Initialize a slice to hold the header
	var headers []string

	// Collect the keys from the Input map and add them to the header
	for key := range e.Input {
		headers = append(headers, key)
	}

	headers = append(headers, "is_error", "error", "input_bid", "output_bid", "change_reason")

	sort.Strings(headers)
	return headers
}

func (e *Estimate) GetValues(headers []string) []string {
	input := e.Input
	input["is_error"] = e.IsError
	input["error"] = e.Error
	input["input_bid"] = e.InputBid
	input["output_bid"] = e.OutputBid
	input["change_reason"] = e.ChangeReason

	var values []string
	for _, header := range headers {
		switch v := input[header].(type) {
		case int, int64, int32:
			values = append(values, fmt.Sprintf("%d", v))
		case float64, float32:
			values = append(values, fmt.Sprintf("%.2f", v)) // Format to 2 decimal places
		case bool:
			values = append(values, fmt.Sprintf("%t", v))
		case map[string]interface{}:
			{
				data, _ := json.Marshal(v)
				values = append(values, string(data))
			}
		case string:
			values = append(values, v)
		default:
			values = append(values, fmt.Sprintf("%v", v))
		}
	}
	return values
}

type QAResult struct {
	Info    string
	IsWarn  bool
	IsError bool
	Error   string
}
