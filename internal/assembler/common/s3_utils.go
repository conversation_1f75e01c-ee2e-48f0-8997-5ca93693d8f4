package common

import (
	"bufio"
	"fmt"
	"os/exec"
	"strings"
	"sync"

	"github.com/rs/zerolog"
	"github.expedia.biz/gmo-performance-marketing/sem-lib-go/pkg/aws"
)

type S3Utils struct {
	logger    zerolog.Logger
	awsClient aws.Client
}

var allowedExts = []string{".parquet"}

func hasAllowedExtension(filename string) bool {
	for _, ext := range allowedExts {
		if strings.HasSuffix(filename, ext) {
			return true
		}
	}
	return false
}

func isDirectory(path string) bool {
	return strings.HasSuffix(path, "/")
}

func NewS3Utils(logger zerolog.Logger, awsClient aws.Client) *S3Utils {
	return &S3Utils{logger: logger, awsClient: awsClient}
}

func (s *S3Utils) ListFiles(s3Path string) ([]aws.S3Object, error) {
	// Run the AWS CLI 'aws s3 ls' command
	cmd := exec.Command("aws", "s3", "ls", s3Path, "--recursive")

	// Capture the output (stdout and stderr)
	output, err := cmd.CombinedOutput()
	if err != nil {
		return nil, fmt.Errorf("error running AWS CLI command: %v\nOutput: %s", err, output)
	}

	// Process the output to extract file paths (keys)
	var filePaths []aws.S3Object
	scanner := bufio.NewScanner(strings.NewReader(string(output)))
	for scanner.Scan() {
		line := scanner.Text()
		// Assuming the output has the format "2025-02-27 18:02:42      18423 file-name.txt"
		// We need the file path (the last part of the line)
		// Example output: "2025-02-27 18:02:42      18423 file-name.txt"
		// Split on spaces to get the file path
		parts := strings.Fields(line)
		if len(parts) > 3 && !isDirectory(parts[3]) && hasAllowedExtension(parts[3]) {
			// Append the full S3 path (S3 bucket + path)
			filePaths = append(filePaths, aws.S3Object{Bucket: &strings.Split(s3Path, "/")[2], Key: &parts[3]})
		}
	}

	if err := scanner.Err(); err != nil {
		return nil, fmt.Errorf("error reading command output: %v", err)
	}

	return filePaths, nil
}

func (s *S3Utils) DownloadFiles(
	localPath string, s3Objects []aws.S3Object,
) error {

	var wg sync.WaitGroup
	errCh := make(chan error, len(s3Objects))

	for _, s3Object := range s3Objects {
		wg.Add(1)
		go func(s3Object aws.S3Object) {
			defer wg.Done()
			if err := s.downloadFileAndHandleAlias(s3Object, localPath); err != nil {
				errCh <- err
			}
		}(s3Object)
	}

	wg.Wait()
	close(errCh)

	// If there was an error during file download, return the first error encountered
	if len(errCh) > 0 {
		return <-errCh
	}

	return nil
}

func (s *S3Utils) DownloadFile(remotePath string, localPath string) (*string, error) {
	s3PathSplit := strings.Split(remotePath, "/")
	fileName := s3PathSplit[len(s3PathSplit)-1]
	localPathWithFileName := fmt.Sprintf("%s/%s", localPath, fileName)
	cmd := exec.Command("aws", "s3", "cp", remotePath, localPath)

	s.logger.Info().Msgf("downloading file %s to %s", fileName, localPathWithFileName)
	// Capture the output (stdout and stderr)
	_, err := cmd.CombinedOutput()
	if err != nil {
		return nil, fmt.Errorf("s3Download: %w", err)
	}
	return &localPathWithFileName, nil
}

func (s *S3Utils) UploadDir(localPath string, remotePath string) error {
	// Build the aws s3 sync command
	cmd := exec.Command("aws", "s3", "sync", localPath, remotePath)
	s.logger.Info().Msgf("uploading %s to %s", localPath, remotePath)
	// Run the command and capture the output
	output, err := cmd.CombinedOutput()
	if err != nil {
		return fmt.Errorf("failed to sync directory: %v, output: %s", err, output)
	}
	return nil
}

func (s *S3Utils) Parse(s3Path string) (*aws.S3Object, error) {
	s3Uri := aws.S3Uri(s3Path)
	return s3Uri.Parse()
}

func (s *S3Utils) downloadFileAndHandleAlias(s3Object aws.S3Object, localPath string) error {
	s3KeySplit := strings.Split(*s3Object.Key, "/")
	fileName := s3KeySplit[len(s3KeySplit)-1]
	objectLocalPath := fmt.Sprintf("%s/%s", localPath, fileName)
	s3Path := fmt.Sprintf("s3://%s/%s", *s3Object.Bucket, *s3Object.Key)
	cmd := exec.Command("aws", "s3", "cp", s3Path, objectLocalPath)

	// Capture the output (stdout and stderr)
	_, err := cmd.CombinedOutput()
	if err != nil {
		return fmt.Errorf("downloadFile: %w", err)
	}
	return nil
}
