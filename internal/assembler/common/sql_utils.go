// Package bidassembly provides functionality for assembling bids based on various
// configurations, filters, enrichers, and rules.
package common

import (
	"regexp"
	"strings"
)

// SanitizeSQLInput escapes single quotes and other potentially dangerous characters
// to help prevent SQL injection attacks. This function should be used whenever
// constructing SQL queries with user-provided or external data.
//
// The function performs the following sanitization:
// 1. Replaces single quotes with double single quotes (SQL escape for single quotes)
// 2. Removes any semicolons to prevent multiple statement execution
// 3. Removes SQL comments to prevent comment-based attacks
//
// Example usage:
//
//	sanitizedInput := SanitizeSQLInput(userProvidedInput)
//	query := fmt.Sprintf("SELECT * FROM table WHERE column = '%s'", sanitizedInput)
func SanitizeSQLInput(input string) string {
	// Replace single quotes with double single quotes (SQL escape for single quotes)
	escaped := strings.ReplaceAll(input, "'", "''")

	// Remove any semicolons to prevent multiple statement execution
	escaped = strings.ReplaceAll(escaped, ";", "")

	// Remove comments
	commentPattern := regexp.MustCompile(`--.*$|/\*.*?\*/`)
	escaped = commentPattern.ReplaceAllString(escaped, "")

	return escaped
}
