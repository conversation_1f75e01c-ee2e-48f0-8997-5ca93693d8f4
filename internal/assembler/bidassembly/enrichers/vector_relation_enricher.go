package enrichers

import (
	"context"
	"database/sql"
	"fmt"
	"os"

	"github.com/Netflix/go-env"
	"github.com/rs/zerolog"
	"github.expedia.biz/gmo-performance-marketing/loki/internal/assembler/common"
	pb "github.expedia.biz/gmo-performance-marketing/pb-go/pkg/loki/v1"
	"github.expedia.biz/gmo-performance-marketing/sem-lib-go/pkg/collections"
)

type VrConfig struct {
	VectorRelationS3Path string `env:"VECTOR_RELATION_S3_PATH,default=s3://eg-marketing-platform-test/loki/meta-vector-relation/partner_name=TRIVAGO/latest/"`
}

type VectorRelationEnricher struct {
	vrConfig       VrConfig
	logger         zerolog.Logger
	enricherConfig *pb.VectorRelationEnricher
	s3Utils        *common.S3Utils
	runParameters  map[string]interface{}
}

func newVectorRelationEnricher(
	logger zerolog.Logger,
	enricherConfig *pb.VectorRelationEnricher,
	s3Utils *common.S3Utils,
	runParameters map[string]interface{},
) (*VectorRelationEnricher, error) {
	var vrConfig VrConfig

	_, err := env.UnmarshalFromEnviron(&vrConfig)
	if err != nil {
		return nil, fmt.Errorf("failed to unmarshal vector relation config: %w", err)
	}
	l := logger.With().Str("component", "vectorRelationEnricher").Logger()
	return &VectorRelationEnricher{
		vrConfig:       vrConfig,
		logger:         l,
		enricherConfig: enricherConfig,
		s3Utils:        s3Utils,
		runParameters:  runParameters}, nil
}

func (v *VectorRelationEnricher) LoadData(db *sql.DB) error {
	vrDataPath := v.vrConfig.VectorRelationS3Path
	_ = os.MkdirAll(v.localPath(), os.ModePerm)
	v.logger.Info().Msgf("downloading vector relation data from %s", vrDataPath)
	s3Objects, err := v.s3Utils.ListFiles(vrDataPath)
	if err != nil {
		return fmt.Errorf("s3List: %w", err)
	}
	err = v.s3Utils.DownloadFiles(v.localPath(), s3Objects)
	if err != nil {
		return fmt.Errorf("s3Download: %w", err)
	}

	query := fmt.Sprintf("CREATE OR REPLACE TABLE %s AS (SELECT * FROM read_parquet('%s/*.parquet', union_by_name = true))", v.TableName(), v.localPath())
	v.logger.Info().Msgf("Running query: %s", query)
	result, err := db.ExecContext(context.Background(), query)
	if err != nil {
		return fmt.Errorf("loadVectorRelationData: %w", err)
	}
	rowsAffected, _ := result.RowsAffected()
	v.logger.Info().Msgf("Successfully loaded vector relation data, rows affected: %d", rowsAffected)

	return nil
}

func (v *VectorRelationEnricher) SupportedMarketingChannels() collections.Set[string] {
	return collections.NewSet("trivago")
}

func (v *VectorRelationEnricher) EnrichData(db *sql.DB, estimatesTable string, bidUnitConfig common.BidUnitConfig) error {
	joinColumn := "partner"
	partnerPosColumn := "partner_pos"
	return enrichDataHelper(db, v.logger, estimatesTable, v.TableName(), joinColumn, partnerPosColumn, v.enricherConfig.EnrichColumns)
}

func (v *VectorRelationEnricher) TableName() string {
	return "vector_relation"
}

func (v *VectorRelationEnricher) localPath() string {
	return "./workspace/vector_relation"
}
