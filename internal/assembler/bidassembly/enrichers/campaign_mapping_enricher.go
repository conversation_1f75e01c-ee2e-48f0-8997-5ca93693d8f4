package enrichers

import (
	"context"
	"database/sql"
	"fmt"
	"os"

	"github.com/Netflix/go-env"
	"github.com/rs/zerolog"
	"github.expedia.biz/gmo-performance-marketing/loki/internal/assembler/common"
	pb "github.expedia.biz/gmo-performance-marketing/pb-go/pkg/loki/v1"
	"github.expedia.biz/gmo-performance-marketing/sem-lib-go/pkg/collections"
)

type config struct {
	CampaignMappingPath string `env:"CAMPAIGN_MAPPING_S3_PATH,default=s3://eg-marketing-platform-test/loki/meta-campaign-mapping/brand_name=%s/partner_date=latest/"`
}

type CampaignMappingEnricher struct {
	campaignMappingConfig config
	logger                zerolog.Logger
	enricherConfig        *pb.CampaignMappingEnricher
	s3Utils               *common.S3Utils
	runParameters         map[string]interface{}
}

func newCampaignMappingEnricher(
	logger zerolog.Logger,
	enricherConfig *pb.CampaignMappingEnricher,
	s3Utils *common.S3Utils,
	runParameters map[string]interface{},
) (*CampaignMappingEnricher, error) {
	var campaignMappingConfig config

	_, err := env.UnmarshalFromEnviron(&campaignMappingConfig)
	if err != nil {
		return nil, fmt.Errorf("failed to unmarshal campaign mapping config: %w", err)
	}
	l := logger.With().Str("component", "campaignMappingEnricher").Logger()
	return &CampaignMappingEnricher{
		campaignMappingConfig: campaignMappingConfig,
		logger:                l,
		enricherConfig:        enricherConfig,
		s3Utils:               s3Utils,
		runParameters:         runParameters}, nil
}

func (c *CampaignMappingEnricher) LoadData(db *sql.DB) error {
	campaignMappingDatapath := c.campaignMappingConfig.CampaignMappingPath
	_ = os.MkdirAll(c.localPath(), os.ModePerm)
	c.logger.Info().Msgf("downloading campaign mapping data from %s", campaignMappingDatapath)
	s3Objects, err := c.s3Utils.ListFiles(campaignMappingDatapath)
	if err != nil {
		return fmt.Errorf("s3List: %w", err)
	}
	err = c.s3Utils.DownloadFiles(c.localPath(), s3Objects)
	if err != nil {
		return fmt.Errorf("s3Download: %w", err)
	}
	c.logger.Info().Msgf("successfully downloaded %d campaign mapping files", len(s3Objects))

	query := fmt.Sprintf("CREATE OR REPLACE TABLE %s AS (SELECT * FROM read_parquet('%s/*.parquet', union_by_name = true))", c.TableName(), c.localPath())
	c.logger.Info().Msgf("Running query: %s", query)
	result, err := db.ExecContext(context.Background(), query)
	if err != nil {
		return fmt.Errorf("loadCampaignMappingData: %w", err)
	}

	rowsAffected, _ := result.RowsAffected()
	c.logger.Info().Msgf("Total campaign mapping rows loaded: %d", rowsAffected)
	return nil
}

func (c *CampaignMappingEnricher) EnrichData(db *sql.DB, estimatesTable string, bidUnitConfig common.BidUnitConfig) error {
	joinColumn := "campaign_name"
	return enrichDataHelper(db, c.logger, estimatesTable, c.TableName(), joinColumn, joinColumn, c.enricherConfig.EnrichColumns)
}

func (c *CampaignMappingEnricher) SupportedMarketingChannels() collections.Set[string] {
	return collections.NewSet("google-hotel-ads", "google-search", "bing-search")
}

func (c *CampaignMappingEnricher) TableName() string {
	return "campaign_mapping"
}

func (c *CampaignMappingEnricher) localPath() string {
	return "./workspace/campaign-mapping"
}
