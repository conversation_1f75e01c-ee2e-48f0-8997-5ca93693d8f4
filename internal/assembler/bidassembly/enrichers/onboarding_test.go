package enrichers

import (
	"database/sql"
	"fmt"
	"testing"

	_ "github.com/marcboeker/go-duckdb/v2"
	"github.com/rs/zerolog"
	"github.expedia.biz/gmo-performance-marketing/loki/internal/assembler/common"
	"github.expedia.biz/gmo-performance-marketing/sem-lib-go/pkg/collections"
)

// This file serves as a guide for onboarding new enrichers.
// It provides a dummy implementation and a test case to illustrate how a new
// enricher can be tested in isolation without running the full assembly job.

// --- Dummy Enricher ---

// 1. Define a struct for your new enricher.
type dummyEnricher struct {
	logger zerolog.Logger
	// Add any fields your enricher needs, like configuration or clients.
}

// 2. Implement the enrichers.Interface.

func (d *dummyEnricher) LoadData(db *sql.DB) error {
	d.logger.Info().Msg("Loading data for dummy enricher")
	// In a real enricher, you would load data from a source (e.g., S3)
	// and create a table in the provided DuckDB instance.
	_, err := db.Exec("CREATE TABLE dummy_enricher_data (id VARCHAR, value VARCHAR);")
	if err != nil {
		return fmt.Errorf("failed to create dummy enricher table: %w", err)
	}
	_, err = db.Exec("INSERT INTO dummy_enricher_data VALUES ('1', 'enriched_value');")
	return err
}

func (d *dummyEnricher) EnrichData(db *sql.DB, estimatesTable string, bidUnitConfig common.BidUnitConfig) error {
	d.logger.Info().Msgf("Enriching data in table %s for bid unit config %s", estimatesTable, bidUnitConfig.Id)
	// This is where you would write the SQL to join your enricher's data
	// with the main estimates table.
	// For this example, we'll just add a new column and update a value.
	_, err := db.Exec(fmt.Sprintf("ALTER TABLE %s ADD COLUMN enriched_column VARCHAR;", estimatesTable))
	if err != nil {
		return err
	}
	_, err = db.Exec(fmt.Sprintf("UPDATE %s SET enriched_column = 'enriched' WHERE id = '1';", estimatesTable))
	return err
}

func (d *dummyEnricher) SupportedMarketingChannels() collections.Set[string] {
	// Specify which marketing channels this enricher supports.
	return collections.NewSet("DUMMY_CHANNEL")
}

func (d *dummyEnricher) TableName() string {
	return "dummy_enricher_data"
}

// 3. Create a constructor for your enricher.
func newDummyEnricher(logger zerolog.Logger) Interface {
	return &dummyEnricher{logger: logger}
}

// --- Test Case ---

func setupEnricherTest(t *testing.T) (*sql.DB, func()) {
	t.Helper()
	db, err := sql.Open("duckdb", "") // In-memory DuckDB
	if err != nil {
		t.Fatalf("Failed to open database: %v", err)
	}

	// Create a dummy estimates table to work with
	_, err = db.Exec("CREATE TABLE estimates (id VARCHAR, bid_price FLOAT);")
	if err != nil {
		t.Fatalf("Failed to create estimates table: %v", err)
	}
	_, err = db.Exec("INSERT INTO estimates VALUES ('1', 1.0), ('2', 2.0);")
	if err != nil {
		t.Fatalf("Failed to insert into estimates table: %v", err)
	}

	tearDown := func() {
		_, err := db.Exec("DROP TABLE estimates;")
		if err != nil {
			t.Fatalf("Failed to drop estimates table: %v", err)
		}
		db.Close()
	}

	return db, tearDown
}

func TestEnricherOnboarding(t *testing.T) {
	l := zerolog.Nop()
	db, tearDown := setupEnricherTest(t)
	defer tearDown()

	// --- Enricher Onboarding ---
	// Use the constructor for your new enricher here.
	enricher := newDummyEnricher(l)

	// 1. Test LoadData
	err := enricher.LoadData(db)
	if err != nil {
		t.Fatalf("Enricher failed to load data: %v", err)
	}
	defer db.Exec(fmt.Sprintf("DROP TABLE %s;", enricher.TableName()))

	// 2. Test EnrichData
	err = enricher.EnrichData(db, "estimates", common.BidUnitConfig{})
	if err != nil {
		t.Fatalf("Enricher failed to enrich data: %v", err)
	}

	// 3. Verify results
	row := db.QueryRow("SELECT enriched_column FROM estimates WHERE id = '1';")
	var enrichedColumn sql.NullString
	err = row.Scan(&enrichedColumn)
	if err != nil {
		t.Fatalf("Failed to scan row: %v", err)
	}

	if !enrichedColumn.Valid || enrichedColumn.String != "enriched" {
		t.Errorf("Expected enriched_column to be 'enriched', but got '%s'", enrichedColumn.String)
	}
}
