package enrichers

import (
	"context"
	"database/sql"
	"fmt"
	"os"

	"github.com/Netflix/go-env"
	"github.com/rs/zerolog"
	"github.expedia.biz/gmo-performance-marketing/loki/internal/assembler/common"
	pb "github.expedia.biz/gmo-performance-marketing/pb-go/pkg/loki/v1"
	"github.expedia.biz/gmo-performance-marketing/sem-lib-go/pkg/collections"
)

type AdrConfig struct {
	AverageDailyRateS3Path string `env:"AVERAGE_DAILY_RATE_S3_PATH,default=s3://eg-marketing-platform-test/loki/meta-average-daily-rate/latest/"`
}

type AverageDailyRateEnricher struct {
	adrConfig      AdrConfig
	logger         zerolog.Logger
	enricherConfig *pb.AverageDailyRateEnricher
	s3Utils        *common.S3Utils
	runParameters  map[string]interface{}
}

func newAverageDailyRateEnricher(
	logger zerolog.Logger,
	enricherConfig *pb.AverageDailyRateEnricher,
	s3Utils *common.S3Utils,
	runParameters map[string]interface{},
) (*AverageDailyRateEnricher, error) {
	var adrConfig AdrConfig

	_, err := env.UnmarshalFromEnviron(&adrConfig)
	if err != nil {
		return nil, fmt.Errorf("failed to unmarshal average daily rate config: %w", err)
	}
	l := logger.With().Str("component", "averageDailyRateEnricher").Logger()
	return &AverageDailyRateEnricher{
		adrConfig:      adrConfig,
		logger:         l,
		enricherConfig: enricherConfig,
		s3Utils:        s3Utils,
		runParameters:  runParameters}, nil
}

func (a *AverageDailyRateEnricher) LoadData(db *sql.DB) error {
	adrDataPath := a.adrConfig.AverageDailyRateS3Path
	_ = os.MkdirAll(a.localPath(), os.ModePerm)
	a.logger.Info().Msgf("downloading average daily rate data from %s", adrDataPath)
	s3Objects, err := a.s3Utils.ListFiles(adrDataPath)
	if err != nil {
		return fmt.Errorf("s3List: %w", err)
	}
	err = a.s3Utils.DownloadFiles(a.localPath(), s3Objects)
	if err != nil {
		return fmt.Errorf("s3Download: %w", err)
	}
	a.logger.Info().Msgf("successfully downloaded %d average daily rate files", len(s3Objects))

	query := fmt.Sprintf("CREATE OR REPLACE TABLE %s AS (SELECT * FROM read_parquet('%s/*.parquet', union_by_name = true))", a.TableName(), a.localPath())
	a.logger.Info().Msgf("Running query: %s", query)
	result, err := db.ExecContext(context.Background(), query)
	if err != nil {
		return fmt.Errorf("loadAverageDailyRateData: %w", err)
	}
	rowsAffected, _ := result.RowsAffected()
	a.logger.Info().Msgf("Loaded %d rows into table %s", rowsAffected, a.TableName())
	return nil
}

func (a *AverageDailyRateEnricher) EnrichData(db *sql.DB, estimatesTable string, bidUnitConfig common.BidUnitConfig) error {
	joinColumn := "hotel_id"
	sourceColumn := "bex_hotel_id"
	return enrichDataHelper(db, a.logger, estimatesTable, a.TableName(), joinColumn, sourceColumn, a.enricherConfig.EnrichColumns)
}

func (a *AverageDailyRateEnricher) SupportedMarketingChannels() collections.Set[string] {
	return collections.NewSet("trivago")
}

func (a *AverageDailyRateEnricher) TableName() string {
	return "average_daily_rate"
}

func (a *AverageDailyRateEnricher) localPath() string {
	return "./workspace/average-daily-rate"
}
