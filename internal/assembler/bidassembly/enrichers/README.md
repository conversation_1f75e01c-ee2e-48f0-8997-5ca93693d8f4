# Enrichers

Enrichers are used to add new data to the bid estimates during the assembly process. Each enricher is responsible for loading its own data and applying the enrichment logic.

## Onboarding a New Enricher

To add a new enricher, follow these steps:

1.  **Implement the Interface**: Create a new `.go` file in this directory for your enricher. In that file, define a struct and implement the `enrichers.Interface`.
    *   `LoadData(*sql.DB) error`: This method should handle loading any necessary data from its source (e.g., a file from S3) and preparing it in the provided in-memory DuckDB database. This usually involves creating a new table and inserting data into it.
    *   `EnrichData(*sql.DB, string, common.BidUnitConfig) error`: This method contains the core enrichment logic. It should execute SQL queries against the database to join the enricher's data with the main `estimatesTable` and add or modify columns as needed.
    *   `SupportedMarketingChannels() collections.Set[string]`: Return a set of marketing channel strings that this enricher supports.
    *   `TableName() string`: Return the name of the table this enricher creates in the database.

2.  **Add to Protobuf**: Add your new enricher to the `Enricher` oneof field in the `loki.proto` file.

3.  **Register the Enricher**: In `internal/assembler/bidassembly/enrichers/interface.go`, add a new case to the `switch` statement in the `GetImplementedEnrichers` function. This will instantiate your enricher when it's specified in a pipeline configuration.

## Testing Locally

You can test your enricher in isolation without running the entire bid assembly job. This allows for faster development and easier debugging.

The `onboarding_test.go` file provides a template for how to write a unit test for your enricher.

To test your enricher:

1.  Create a new test function in `onboarding_test.go` or a new `_test.go` file.
2.  Create an in-memory DuckDB instance.
3.  Create a dummy `estimates` table with sample data that mimics the real data your enricher will encounter.
4.  Instantiate your enricher using its constructor (e.g., `newMyEnricher()`).
5.  Call the `LoadData` method and verify that the data was loaded correctly into the database.
6.  Call the `EnrichData` method.
7.  Query the `estimates` table and assert that the data was enriched as expected.

You can run the tests from the command line:

```bash
go test ./internal/assembler/bidassembly/enrichers/...
```
