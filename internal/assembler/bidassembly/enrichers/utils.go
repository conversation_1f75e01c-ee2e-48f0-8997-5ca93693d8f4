// Package enrichers provides functionality for enriching bid data with additional
// information from various sources. Enrichers implement the Interface defined in
// this package and can be registered to be used in the bid assembly process.
package enrichers

import (
	"context"
	"database/sql"
	"fmt"

	"github.com/rs/zerolog"
	"github.expedia.biz/gmo-performance-marketing/loki/internal/assembler/common"
	lokiv1 "github.expedia.biz/gmo-performance-marketing/pb-go/pkg/loki/v1"
)

// EnrichDataHelper is a helper function to reduce code duplication in enrichers.
// It adds enrichColumns to a metadata JSON field in the estimates table instead of creating columns.
//
// This function performs the following operations:
// 1. Builds a JSON update expression to add enrichColumns to the metadata field
// 2. Executes an UPDATE statement to enrich the metadata with values from the source table
//
// All SQL inputs are sanitized to prevent SQL injection attacks.
//
// Parameters:
//   - db: The database connection
//   - logger: The logger to use for logging
//   - estimatesTable: The name of the table containing the estimates
//   - sourceTable: The name of the table containing the source data
//   - joinColumn: The column in the estimates table to join on
//   - sourceJoinColumn: The column in the source table to join on
//   - enrichColumns: The columns to add to the metadata JSON field
//   - bidUnitConfig: The bid unit configuration
//
// Returns:
//   - error: An error if any operation fails
//
// Example usage:
//
//	err := EnrichDataHelper(
//	    db,
//	    logger,
//	    "estimates",
//	    "campaign_mapping",
//	    "campaign_id",
//	    "campaign_id",
//	    []struct{Name string; DataType string}{
//	        {Name: "campaign_name", DataType: "VARCHAR"},
//	        {Name: "campaign_type", DataType: "VARCHAR"},
//	    },
//	    bidUnitConfig,
//	)
func enrichDataHelper(
	db *sql.DB,
	logger zerolog.Logger,
	estimatesTable string,
	sourceTable string,
	joinColumn string,
	sourceJoinColumn string,
	enrichColumns []*lokiv1.EnrichColumn,
) error {

	// If no enrichColumns, nothing to update
	if len(enrichColumns) == 0 {
		logger.Info().Msg("No enrichColumns provided, skipping update")
		return nil
	}

	sanitizedJoinColumn := common.SanitizeSQLInput(joinColumn)
	sanitizedSourceJoinColumn := common.SanitizeSQLInput(sourceJoinColumn)
	sanitizedEstimatesTable := common.SanitizeSQLInput(estimatesTable)
	sanitizedSourceTable := common.SanitizeSQLInput(sourceTable)

	// Execute one UPDATE statement per enrichColumn to avoid issues with multiple updates to the same column
	var totalRowsAffected int64
	for _, column := range enrichColumns {
		sanitizedColumnName := common.SanitizeSQLInput(column.Name)

		// Create a JSON update expression for this column
		updateExpression := fmt.Sprintf("assembler_metadata = json_merge_patch(COALESCE(assembler_metadata, '{}'), json_object('%s', %s.%s))",
			sanitizedColumnName, sanitizedSourceTable, sanitizedColumnName)

		// Build the UPDATE query for this column
		updateQuery := fmt.Sprintf("UPDATE %s a SET %s FROM %s WHERE a.%s = %s.%s",
			sanitizedEstimatesTable, updateExpression, sanitizedSourceTable,
			sanitizedJoinColumn, sanitizedSourceTable, sanitizedSourceJoinColumn)

		logger.Debug().Msgf("enrichData for column %s: %s", column.Name, updateQuery)

		// Execute the UPDATE query
		res, err := db.ExecContext(context.Background(), updateQuery)
		if err != nil {
			logger.Error().Err(err).Msgf("enrichData: failed to update metadata with column %s", column.Name)
			return err
		}

		rowsAffected, _ := res.RowsAffected()
		totalRowsAffected += rowsAffected
		logger.Debug().Msgf("column %s: rows affected: %d", column.Name, rowsAffected)
	}

	logger.Info().Msgf("total rows affected across all updates: %d", totalRowsAffected)
	return nil
}
