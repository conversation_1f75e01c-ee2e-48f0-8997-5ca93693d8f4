package enrichers

import (
	"database/sql"
	"fmt"

	"github.com/rs/zerolog"
	"github.expedia.biz/gmo-performance-marketing/loki/internal/assembler/common"
	pb "github.expedia.biz/gmo-performance-marketing/pb-go/pkg/loki/v1"
	"github.expedia.biz/gmo-performance-marketing/sem-lib-go/pkg/collections"
)

type Interface interface {
	LoadData(db *sql.DB) error
	EnrichData(db *sql.DB, estimatesTable string, bidUnitConfig common.BidUnitConfig) error
	SupportedMarketingChannels() collections.Set[string]
	TableName() string
}

func GetImplementedEnrichers(config *pb.PipelineAssemblerConfig, s3Utils *common.S3Utils, logger zerolog.Logger, runParameters map[string]interface{}) ([]Interface, error) {
	enrichers := []Interface{}
	for _, enricher := range config.Enrichers {
		switch enricher.Enricher.(type) {
		case *pb.Enricher_PropertyInventoryEnricher:
			enrichers = append(enrichers, newPropertyInventoryEnricher(logger, enricher.GetPropertyInventoryEnricher()))

		case *pb.Enricher_CampaignMappingEnricher:
			campaignMappingEnricher, err := newCampaignMappingEnricher(logger, enricher.GetCampaignMappingEnricher(), s3Utils, runParameters)
			if err != nil {
				return nil, err
			}
			enrichers = append(enrichers, campaignMappingEnricher)

		case *pb.Enricher_AverageDailyRateEnricher:
			averageDailyRateEnricher, err := newAverageDailyRateEnricher(logger, enricher.GetAverageDailyRateEnricher(), s3Utils, runParameters)
			if err != nil {
				return nil, err
			}
			enrichers = append(enrichers, averageDailyRateEnricher)

		case *pb.Enricher_VectorRelationEnricher:
			vectorRelationEnricher, err := newVectorRelationEnricher(logger, enricher.GetVectorRelationEnricher(), s3Utils, runParameters)
			if err != nil {
				return nil, err
			}
			enrichers = append(enrichers, vectorRelationEnricher)

		default:
			return nil, fmt.Errorf("unknown enricher type: %T", enricher.Enricher)
		}
	}
	return enrichers, nil
}
