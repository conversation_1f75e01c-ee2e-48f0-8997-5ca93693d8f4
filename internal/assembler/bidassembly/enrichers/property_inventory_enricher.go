package enrichers

import (
	"database/sql"

	"github.com/rs/zerolog"
	"github.expedia.biz/gmo-performance-marketing/loki/internal/assembler/common"
	pb "github.expedia.biz/gmo-performance-marketing/pb-go/pkg/loki/v1"
	"github.expedia.biz/gmo-performance-marketing/sem-lib-go/pkg/collections"
)

type PropertyInventoryEnricher struct {
	logger         zerolog.Logger
	enricherConfig *pb.PropertyInventoryEnricher
}

func newPropertyInventoryEnricher(logger zerolog.Logger, enricherConfig *pb.PropertyInventoryEnricher) *PropertyInventoryEnricher {
	l := logger.With().Str("component", "property_inventory_enricher").Logger()
	return &PropertyInventoryEnricher{logger: l, enricherConfig: enricherConfig}
}

// if you are using inventory data if would have been loaded as part of filtering, so this is null
func (p *PropertyInventoryEnricher) LoadData(db *sql.DB) error {
	return nil
}

func (p *PropertyInventoryEnricher) EnrichData(db *sql.DB, estimatesTable string, bidUnitConfig common.BidUnitConfig) error {
	propertyIdColumn := "eg_property_id"
	joinColumn := "hotel_id"
	return enrichDataHelper(db, p.logger, estimatesTable, p.TableName(), joinColumn, propertyIdColumn, p.enricherConfig.EnrichColumns)
}

func (p *PropertyInventoryEnricher) SupportedMarketingChannels() collections.Set[string] {
	supportedMarketingChannels := collections.Set[string]{}
	supportedMarketingChannels.AddAll(
		"google-hotel-ads", "google-search", "bing-search", "trip-advisor", "cheap-flights", "kayak", "trivago")
	return supportedMarketingChannels
}

func (p *PropertyInventoryEnricher) TableName() string {
	return "property_inventory"
}
