package rules

import (
	"encoding/json"
	"testing"

	"github.com/rs/zerolog"
	"github.com/stretchr/testify/assert"
	"github.expedia.biz/gmo-performance-marketing/loki/internal/assembler/common"
)

func TestNewGha(t *testing.T) {
	// Create a test rule
	rule := common.AssemblerRule{
		RuleSchema: "test_schema",
		RuleId:     "test_id",
		RuleName:   "test_name",
		RuleItems: []common.RuleItem{
			{
				Id:   "1",
				Info: createRuleItemInfo("factor", 1.5, 0, 0, 1, nil, "base_bid"),
			},
			{
				Id:   "2",
				Info: createRuleItemInfo("minmax", 0, 0.5, 2.0, 2, nil, "base_bid"),
			},
		},
	}

	// Create a logger
	logger := zerolog.New(zerolog.NewTestWriter(t))

	// Create a new gha instance
	ghaRule, err := newGha(rule, logger)

	// Assert no error
	assert.NoError(t, err)

	// Assert the gha instance is correctly initialized
	assert.Equal(t, "test_schema", ghaRule.RuleName())
	assert.True(t, ghaRule.SupportedMarketingChannels().Contains("google-hotel-ads"))
}

func TestNewGhaWithEmptySchema(t *testing.T) {
	// Create a test rule with empty schema
	rule := common.AssemblerRule{
		RuleSchema: "",
		RuleId:     "test_id",
		RuleName:   "test_name",
	}

	// Create a logger
	logger := zerolog.New(zerolog.NewTestWriter(t))

	// Create a new gha instance
	_, err := newGha(rule, logger)

	// Assert error
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "rule schema cannot be empty")
}

func TestApplyRuleWithFactorNoMatch(t *testing.T) {
	// Create a test rule with a factor rule that has dimensions that won't match
	rule := common.AssemblerRule{
		RuleSchema: "test_schema",
		RuleId:     "test_id",
		RuleName:   "test_name",
		RuleItems: []common.RuleItem{
			{
				Id: "1",
				Info: createRuleItemInfo("factor", 1.5, 0, 0, 1, []dimension{
					{Name: "country", Value: "US"},
				}, "platform"),
			},
		},
	}

	// Create a logger
	logger := zerolog.New(zerolog.NewTestWriter(t))

	// Create a new gha instance
	ghaRule, err := newGha(rule, logger)
	assert.NoError(t, err)

	// Create an estimate with different country
	estimate := &common.Estimate{
		Input: map[string]interface{}{
			"bid_unit_type":    "platform",
			"multiplier_level": "desktop",
			"multiplier_value": float64(1.0),
		},
		EnrichedData: map[string]interface{}{
			"country": "CA", // Different from the rule
		},
	}

	// Apply the rule
	ghaRule.ApplyRule(estimate)

	// Assert the bid is unchanged (no rule applied)
	assert.Equal(t, float64(1.0), estimate.OutputBid)
	assert.Equal(t, "No rules applied", estimate.ChangeReason)
}

func TestApplyRuleWithFactorMatch(t *testing.T) {
	// Create a test rule with a factor rule that has dimensions that will match
	rule := common.AssemblerRule{
		RuleSchema: "test_schema",
		RuleId:     "test_id",
		RuleName:   "test_name",
		RuleItems: []common.RuleItem{
			{
				Id: "1",
				Info: createRuleItemInfo("factor", 1.5, 0, 0, 1, []dimension{
					{Name: "country", Value: "US"},
				}, "platform"),
			},
		},
	}

	// Create a logger
	logger := zerolog.New(zerolog.NewTestWriter(t))

	// Create a new gha instance
	ghaRule, err := newGha(rule, logger)
	assert.NoError(t, err)

	// Create an estimate with matching country
	estimate := &common.Estimate{
		Input: map[string]interface{}{
			"bid_unit_type":    "platform",
			"multiplier_level": "desktop",
			"multiplier_value": float64(1.0),
		},
		EnrichedData: map[string]interface{}{
			"country": "US", // Matches the rule
		},
	}

	// Apply the rule
	ghaRule.ApplyRule(estimate)

	// Assert the bid is changed by the factor
	assert.Equal(t, float64(1.5), estimate.OutputBid)
	assert.Contains(t, estimate.ChangeReason, "Applied factor rule: 1.50 for dimensions [country=US]")
}

func TestApplyRuleWithGlobalFactor(t *testing.T) {
	// Create a test rule with a global factor rule (no dimensions)
	rule := common.AssemblerRule{
		RuleSchema: "test_schema",
		RuleId:     "test_id",
		RuleName:   "test_name",
		RuleItems: []common.RuleItem{
			{
				Id:   "1",
				Info: createRuleItemInfo("factor", 1.5, 0, 0, 1, nil, "platform"),
			},
		},
	}

	// Create a logger
	logger := zerolog.New(zerolog.NewTestWriter(t))

	// Create a new gha instance
	ghaRule, err := newGha(rule, logger)
	assert.NoError(t, err)

	// Create an estimate
	estimate := &common.Estimate{
		Input: map[string]interface{}{
			"bid_unit_type":    "platform",
			"multiplier_level": "desktop",
			"multiplier_value": float64(1.0),
		},
		EnrichedData: map[string]interface{}{},
	}

	// Apply the rule
	ghaRule.ApplyRule(estimate)

	// Assert the bid is changed by the global factor
	assert.Equal(t, float64(1.5), estimate.OutputBid)
	assert.Contains(t, estimate.ChangeReason, "Applied global factor rule: 1.50")
}

func TestApplyRuleWithMinimum(t *testing.T) {
	// Create a test rule with a min-max rule
	rule := common.AssemblerRule{
		RuleSchema: "test_schema",
		RuleId:     "test_id",
		RuleName:   "test_name",
		RuleItems: []common.RuleItem{
			{
				Id:   "1",
				Info: createRuleItemInfo("minmax", 0, 0.5, 2.0, 1, nil, "platform"),
			},
		},
	}

	// Create a logger
	logger := zerolog.New(zerolog.NewTestWriter(t))

	// Create a new gha instance
	ghaRule, err := newGha(rule, logger)
	assert.NoError(t, err)

	// Create an estimate with a bid below the minimum
	estimate := &common.Estimate{
		Input: map[string]interface{}{
			"bid_unit_type":    "platform",
			"multiplier_level": "desktop",
			"multiplier_value": float64(0.3), // Below minimum of 0.5
		},
		EnrichedData: map[string]interface{}{},
	}

	// Apply the rule
	ghaRule.ApplyRule(estimate)

	// Assert the bid is increased to the minimum
	assert.Equal(t, float64(0.5), estimate.OutputBid)
	assert.Contains(t, estimate.ChangeReason, "Applied minimum rule: 0.50")
}

func TestApplyRuleWithMaximum(t *testing.T) {
	// Create a test rule with a min-max rule
	rule := common.AssemblerRule{
		RuleSchema: "test_schema",
		RuleId:     "test_id",
		RuleName:   "test_name",
		RuleItems: []common.RuleItem{
			{
				Id:   "1",
				Info: createRuleItemInfo("minmax", 0, 0.5, 2.0, 1, nil, "platform"),
			},
		},
	}

	// Create a logger
	logger := zerolog.New(zerolog.NewTestWriter(t))

	// Create a new gha instance
	ghaRule, err := newGha(rule, logger)
	assert.NoError(t, err)

	// Create an estimate with a bid above the maximum
	estimate := &common.Estimate{
		Input: map[string]interface{}{
			"bid_unit_type":    "platform",
			"multiplier_level": "desktop",
			"multiplier_value": float64(2.5), // Above maximum of 2.0
		},
		EnrichedData: map[string]interface{}{},
	}

	// Apply the rule
	ghaRule.ApplyRule(estimate)

	// Assert the bid is decreased to the maximum
	assert.Equal(t, float64(2.0), estimate.OutputBid)
	assert.Contains(t, estimate.ChangeReason, "Applied maximum rule: 2.00")
}

func TestApplyRuleWithMissingBidUnitType(t *testing.T) {
	// Create a test rule
	rule := common.AssemblerRule{
		RuleSchema: "test_schema",
		RuleId:     "test_id",
		RuleName:   "test_name",
		RuleItems:  []common.RuleItem{},
	}

	// Create a logger
	logger := zerolog.New(zerolog.NewTestWriter(t))

	// Create a new gha instance
	ghaRule, err := newGha(rule, logger)
	assert.NoError(t, err)

	// Create an estimate with missing bid_unit_type
	estimate := &common.Estimate{
		Input:        map[string]interface{}{},
		EnrichedData: map[string]interface{}{},
	}

	// Apply the rule
	ghaRule.ApplyRule(estimate)

	// Assert error
	assert.True(t, estimate.IsError)
	assert.Equal(t, "`bid_unit_type` is required", estimate.Error)
}

func TestApplyRuleWithInvalidBidUnitType(t *testing.T) {
	// Create a test rule
	rule := common.AssemblerRule{
		RuleSchema: "test_schema",
		RuleId:     "test_id",
		RuleName:   "test_name",
		RuleItems:  []common.RuleItem{},
	}

	// Create a logger
	logger := zerolog.New(zerolog.NewTestWriter(t))

	// Create a new gha instance
	ghaRule, err := newGha(rule, logger)
	assert.NoError(t, err)

	// Create an estimate with invalid bid_unit_type
	estimate := &common.Estimate{
		Input: map[string]interface{}{
			"bid_unit_type": "invalid",
		},
		EnrichedData: map[string]interface{}{},
	}

	// Apply the rule
	ghaRule.ApplyRule(estimate)

	// Assert error
	assert.True(t, estimate.IsError)
	assert.Equal(t, "invalid `bid_unit_type`: invalid", estimate.Error)
}

func TestApplyRuleWithMissingMultiplierValue(t *testing.T) {
	// Create a test rule
	rule := common.AssemblerRule{
		RuleSchema: "test_schema",
		RuleId:     "test_id",
		RuleName:   "test_name",
		RuleItems:  []common.RuleItem{},
	}

	// Create a logger
	logger := zerolog.New(zerolog.NewTestWriter(t))

	// Create a new gha instance
	ghaRule, err := newGha(rule, logger)
	assert.NoError(t, err)

	// Create an estimate with missing multiplier_value
	estimate := &common.Estimate{
		Input: map[string]interface{}{
			"bid_unit_type": "platform",
		},
		EnrichedData: map[string]interface{}{},
	}

	// Apply the rule
	ghaRule.ApplyRule(estimate)

	// Assert error
	assert.True(t, estimate.IsError)
	assert.Equal(t, "missing `multiplier_value` in input", estimate.Error)
}

func TestApplyRuleWithMissingBaseBidValue(t *testing.T) {
	// Create a test rule
	rule := common.AssemblerRule{
		RuleSchema: "test_schema",
		RuleId:     "test_id",
		RuleName:   "test_name",
		RuleItems:  []common.RuleItem{},
	}

	// Create a logger
	logger := zerolog.New(zerolog.NewTestWriter(t))

	// Create a new gha instance
	ghaRule, err := newGha(rule, logger)
	assert.NoError(t, err)

	// Create an estimate with missing base_bid_value
	estimate := &common.Estimate{
		Input: map[string]interface{}{
			"bid_unit_type": "base_bid",
		},
		EnrichedData: map[string]interface{}{},
	}

	// Apply the rule
	ghaRule.ApplyRule(estimate)

	// Assert error
	assert.True(t, estimate.IsError)
	assert.Equal(t, "missing base_bid_value in input", estimate.Error)
}

func TestApplyRuleWithMissingEnrichedData(t *testing.T) {
	// Create a test rule with a factor rule that requires enriched data
	rule := common.AssemblerRule{
		RuleSchema: "test_schema",
		RuleId:     "test_id",
		RuleName:   "test_name",
		RuleItems: []common.RuleItem{
			{
				Id: "1",
				Info: createRuleItemInfo("factor", 1.5, 0, 0, 1, []dimension{
					{Name: "country", Value: "US"},
				}, "platform"),
			},
		},
	}

	// Create a logger
	logger := zerolog.New(zerolog.NewTestWriter(t))

	// Create a new gha instance
	ghaRule, err := newGha(rule, logger)
	assert.NoError(t, err)

	// Create an estimate with missing enriched data
	estimate := &common.Estimate{
		Input: map[string]interface{}{
			"bid_unit_type":    "platform",
			"multiplier_level": "desktop",
			"multiplier_value": float64(1.0),
		},
		EnrichedData: map[string]interface{}{
			// Missing "country"
		},
	}

	// Apply the rule
	ghaRule.ApplyRule(estimate)

	// Assert error
	assert.True(t, estimate.IsError)
	assert.Equal(t, "missing `country` in input", estimate.Error)
}

// Helper function to create rule item info JSON
func createRuleItemInfo(operator string, factor float64, minimum float64, maximum float64, priority int, dimensions []dimension, resource string) string {
	item := ruleItem{
		Operator:  operator,
		Factor:    factor,
		Minimum:   minimum,
		Maximum:   maximum,
		Priority:  priority,
		Dimension: dimensions,
		Resource:  resource, // Add a resource value
	}

	infoBytes, _ := json.Marshal(item)
	return string(infoBytes)
}
