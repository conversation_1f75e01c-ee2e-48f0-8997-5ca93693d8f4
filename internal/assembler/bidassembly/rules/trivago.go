package rules

import (
	"encoding/json"
	"fmt"
	"math"
	"sort"
	"strings"

	"github.com/rs/zerolog"
	"github.expedia.biz/gmo-performance-marketing/loki/internal/assembler/common"
	_ "github.expedia.biz/gmo-performance-marketing/loki/internal/rule"
	"github.expedia.biz/gmo-performance-marketing/sem-lib-go/pkg/collections"
)

var trivagoBidUnitTypes = collections.Set[string]{"base_bid": {}, "default_search": {}, "length_of_stay": {}, "advance_booking_window": {}, "guests": {}}

type trivago struct {
	id                string
	marketingChannels collections.Set[string]
	name              string
	logger            zerolog.Logger
	factorRuleItems   []ruleItem
	minmaxRuleItems   []ruleItem
}

func newTrivago(rule common.AssemblerRule, logger zerolog.Logger) (Interface, error) {
	// Validate the rule configuration
	if rule.RuleSchema == "" {
		return nil, fmt.Errorf("rule schema cannot be empty")
	}

	l := logger.With().Str("rule", rule.RuleName).Logger()
	trivago := &trivago{
		id:                rule.RuleId,
		name:              rule.RuleSchema,
		marketingChannels: collections.NewSet("trivago"),
		logger:            l,
	}
	err := trivago.splitAndSortRules(rule.RuleItems)
	if err != nil {
		return nil, err
	}
	return trivago, nil
}

func (r *trivago) ApplyRule(estimate *common.Estimate) {
	// TODO: Double check the applied rules from Vulcan and adjust the rules here.
	inpBidUnitType, ok := estimate.Input["bid_unit_type"]
	if !ok || inpBidUnitType == "" {
		estimate.IsError = true
		estimate.Error = "`bid_unit_type` is required"
		return
	}

	resource, ok := inpBidUnitType.(string)
	if !ok {
		estimate.IsError = true
		estimate.Error = "unable to convert `bid_unit_type` to string"
		return
	}
	if !trivagoBidUnitTypes.Contains(resource) {
		estimate.IsError = true
		estimate.Error = fmt.Sprintf("invalid `bid_unit_type`: %s", resource)
		return
	}

	var inputBid float64
	if resource == "base_bid" {
		baseBid, ok := estimate.Input["base_bid_value"]
		if !ok {
			estimate.IsError = true
			estimate.Error = "missing base_bid_value in input"
			return
		}
		inputBid, ok = baseBid.(float64)
		if !ok {
			estimate.IsError = true
			estimate.Error = "invalid `base_bid_value`"
			return
		}

		// Convert bid based on currency type
		currencyType, ok := estimate.Input["currency_type"]
		if !ok {
			estimate.IsError = true
			estimate.Error = "missing `currency_type` in input"
			return
		}
		convertedBid, err := convertBidByCurrencyType(inputBid, currencyType)
		if err != nil {
			estimate.IsError = true
			estimate.Error = fmt.Sprintf("currency conversion error: %v", err)
			return
		}
		inputBid = convertedBid
	} else {
		multiplierValue, ok := estimate.Input["multiplier_value"]
		if !ok {
			estimate.IsError = true
			estimate.Error = "missing `multiplier_value` in input"
			return
		}

		inputBid, ok = multiplierValue.(float64)
		if !ok {
			estimate.IsError = true
			estimate.Error = "invalid `multiplier_value` in input"
			return
		}
	}

	estimate.InputBid = inputBid

	var appliedRules []string

	for _, item := range r.factorRuleItems {
		if item.Resource != resource {
			continue
		}
		if len(item.Dimension) == 0 {
			oldBid := inputBid
			inputBid = inputBid * item.Factor
			appliedRules = append(appliedRules, fmt.Sprintf("Applied global factor rule: %.2f, changed bid from %.2f to %.2f",
				item.Factor, oldBid, inputBid))
			continue
		}

		isMatch := true
		var dimMatches []string
		for _, dim := range item.Dimension {
			inpVal, inpOk := estimate.Input[dim.Name]
			enrichVal, enrichOk := estimate.EnrichedData[dim.Name]
			if !inpOk && !enrichOk {
				estimate.IsError = true
				estimate.Error = fmt.Sprintf("missing `%s` in input", dim.Name)
				return
			}

			var dimVal string
			var ok bool

			if inpOk {
				dimVal, ok = inpVal.(string)
			} else {
				dimVal, ok = enrichVal.(string)
			}
			if !ok {
				estimate.IsError = true
				estimate.Error = fmt.Sprintf("invalid `%s` in input", dim.Name)
				return
			}

			if dim.Value != dimVal {
				isMatch = false
				break
			}
			dimMatches = append(dimMatches, fmt.Sprintf("%s=%s", dim.Name, dim.Value))
		}

		if isMatch {
			oldBid := inputBid
			inputBid = inputBid * item.Factor
			appliedRules = append(appliedRules, fmt.Sprintf("Applied factor rule: %.2f for dimensions [%s], changed bid from %.2f to %.2f",
				item.Factor,
				strings.Join(dimMatches, ", "),
				oldBid,
				inputBid))
			break
		}
	}

	for _, item := range r.minmaxRuleItems {
		if item.Resource != resource {
			continue
		}
		oldBid := inputBid
		if inputBid < item.Minimum {
			inputBid = item.Minimum
			appliedRules = append(appliedRules, fmt.Sprintf("Applied minimum rule: %.2f, changed bid from %.2f to %.2f",
				item.Minimum, oldBid, inputBid))
			break
		}
		if inputBid > item.Maximum {
			inputBid = item.Maximum
			appliedRules = append(appliedRules, fmt.Sprintf("Applied maximum rule: %.2f, changed bid from %.2f to %.2f",
				item.Maximum, oldBid, inputBid))
			break
		}
	}

	if len(appliedRules) > 0 {
		estimate.ChangeReason = strings.Join(appliedRules, "; ")
	} else {
		estimate.ChangeReason = "No rules applied"
	}

	estimate.OutputBid = inputBid
}

func (r *trivago) RuleName() string {
	return r.name
}

func (r *trivago) SupportedMarketingChannels() collections.Set[string] {
	return r.marketingChannels
}

func init() {
	registerRule("trivago", newTrivago)
}

func (g *trivago) splitAndSortRules(ruleItems []common.RuleItem) error {
	var factorRules []ruleItem
	var minmaxRules []ruleItem

	for _, item := range ruleItems {
		var info ruleItem
		if err := json.Unmarshal([]byte(item.Info), &info); err != nil {
			return fmt.Errorf("failed to unmarshal rule info: %w", err)
		}

		if info.Operator == "factor" {
			factorRules = append(factorRules, info)
			continue
		}
		minmaxRules = append(minmaxRules, info)
	}

	// Sort all factor rules by priority
	sort.Slice(factorRules, func(i, j int) bool {
		return factorRules[i].Priority < factorRules[j].Priority
	})
	// Sort all min-max rules by priority
	sort.Slice(minmaxRules, func(i, j int) bool {
		return minmaxRules[i].Priority < minmaxRules[j].Priority
	})
	g.factorRuleItems = factorRules
	g.minmaxRuleItems = minmaxRules
	return nil
}

// convertBidByCurrencyType converts inputBid based on the currency type. Adopted from Vulcan.
// If currencyType is "EURO_CENTS", it casts inputBid to integer
// If currencyType is "EUR", it applies the conversion equivalent to SQL: CAST(ROUND(100.0 * base_bid_value, 0) AS INT)
func convertBidByCurrencyType(inputBid float64, currencyType interface{}) (float64, error) {
	currencyStr, ok := currencyType.(string)
	if !ok {
		return inputBid, fmt.Errorf("invalid currency_type: expected string, got %T", currencyType)
	}

	switch currencyStr {
	case "EURO_CENTS":
		// Cast to integer (truncate decimal part)
		return float64(int(inputBid)), nil
	case "EUR":
		// Equivalent to SQL: CAST(ROUND(100.0 * base_bid_value, 0) AS INT)
		// Round to nearest integer after multiplying by 100
		return float64(int(math.Round(100.0 * inputBid))), nil
	default:
		// For unknown currency types, return the original value
		return inputBid, nil
	}
}
