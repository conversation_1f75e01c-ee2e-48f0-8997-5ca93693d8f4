// Package rules provides functionality for applying rules to bid data.
// Rules implement the Interface defined in this package and can be
// registered to be used in the bid assembly process.
package rules

import (
	"fmt"

	"github.com/rs/zerolog"
	"github.expedia.biz/gmo-performance-marketing/loki/internal/assembler/common"
	"github.expedia.biz/gmo-performance-marketing/sem-lib-go/pkg/collections"
)

// Interface defines the contract that all rules must implement.
// Rules are used to process and potentially modify estimates during
// the bid assembly process.
type Interface interface {
	// ApplyRule applies the rule to the given estimate.
	// This method should modify the estimate in place if needed.
	ApplyRule(estimate *common.Estimate)

	// RuleName returns the name of the rule.
	// This is used for logging and identification purposes.
	RuleName() string

	// SupportedMarketingChannels returns the set of marketing channels
	// that this rule supports. This is used to determine if the rule
	// should be applied to a given bid unit configuration.
	SupportedMarketingChannels() collections.Set[string]
}

// implementedRulesRegistry is a map of rule names to factory functions.
var implementedRulesRegistry map[string]func(common.AssemblerRule, zerolog.Logger) (Interface, error)

// GetImplementedRules returns a map of rule schema names to rule interfaces.
// It creates instances of the appropriate rule implementations based on the
// provided rule configurations.
//
// Parameters:
//   - rules: A slice of AssemblerRule configurations
//   - logger: The logger to use for logging
//
// Returns:
//   - map[string]Interface: A map of rule schema names to rule interfaces
//   - error: An error if any rule schema is not implemented or if rule creation fails
func GetImplementedRules(rules []common.AssemblerRule, logger zerolog.Logger) (map[string]Interface, error) {
	logger.Info().Msgf("getting rule implementations")
	ruleSchemaToInterface := make(map[string]Interface)
	for _, rule := range rules {
		implementedRule, ok := implementedRulesRegistry[rule.RuleSchema]
		if !ok {
			return nil, fmt.Errorf("ruleSchema: %s is not implemented", rule.RuleSchema)
		}
		ruleInterface, err := implementedRule(rule, logger)
		if err != nil {
			return nil, err
		}
		ruleSchemaToInterface[rule.RuleSchema] = ruleInterface
	}

	return ruleSchemaToInterface, nil
}

// registerRule adds a rule factory function to the implementedRules map.
func registerRule(name string, factory func(common.AssemblerRule, zerolog.Logger) (Interface, error)) {
	if implementedRulesRegistry == nil {
		implementedRulesRegistry = make(map[string]func(common.AssemblerRule, zerolog.Logger) (Interface, error))
	}
	implementedRulesRegistry[name] = factory
}
