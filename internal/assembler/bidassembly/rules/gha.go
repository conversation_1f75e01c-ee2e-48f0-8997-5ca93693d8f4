package rules

import (
	"encoding/json"
	"fmt"
	"sort"
	"strings"

	"github.com/rs/zerolog"
	"github.expedia.biz/gmo-performance-marketing/loki/internal/assembler/common"
	_ "github.expedia.biz/gmo-performance-marketing/loki/internal/rule"
	"github.expedia.biz/gmo-performance-marketing/sem-lib-go/pkg/collections"
)

var bidUnitTypes = collections.Set[string]{"base_bid": {}, "platform": {}, "check_in_day_of_week": {}, "partner_pos": {}, "advance_booking_window": {}, "length_of_stay": {}, "default_search": {}}

type gha struct {
	id                string
	marketingChannels collections.Set[string]
	name              string
	logger            zerolog.Logger

	factorRuleItems []ruleItem
	minmaxRuleItems []ruleItem
}

// Constructor function for the rule
func newGha(rule common.AssemblerRule, logger zerolog.Logger) (Interface, error) {
	// Validate the rule configuration
	if rule.RuleSchema == "" {
		return nil, fmt.Errorf("rule schema cannot be empty")
	}

	l := logger.With().Str("rule", rule.RuleName).Logger()
	gha := &gha{
		id:                rule.RuleId,
		name:              rule.RuleSchema,
		marketingChannels: collections.NewSet("google-hotel-ads"),
		logger:            l,
	}
	err := gha.splitAndSortRules(rule.RuleItems)
	if err != nil {
		return nil, err
	}
	return gha, nil
}

// Implement Interface methods
func (r *gha) ApplyRule(estimate *common.Estimate) {
	inpBidUnitType, ok := estimate.Input["bid_unit_type"]
	if !ok || inpBidUnitType == "" {
		estimate.IsError = true
		estimate.Error = "`bid_unit_type` is required"
		return
	}

	resource, ok := inpBidUnitType.(string)
	if !ok {
		estimate.IsError = true
		estimate.Error = "unable to convert `bid_unit_type` to string"
		return
	}
	if !bidUnitTypes.Contains(resource) {
		estimate.IsError = true
		estimate.Error = fmt.Sprintf("invalid `bid_unit_type`: %s", resource)
		return
	}

	var inputBid float64
	if resource == "base_bid" {
		baseBid, ok := estimate.Input["base_bid_value"]
		if !ok {
			estimate.IsError = true
			estimate.Error = "missing base_bid_value in input"
			return
		}
		inputBid, ok = baseBid.(float64)
		if !ok {
			estimate.IsError = true
			estimate.Error = "invalid `base_bid_value`"
			return
		}
		// converting from cents to dollars
		inputBid = inputBid / 100
	} else {
		multiplierValue, ok := estimate.Input["multiplier_value"]
		if !ok {
			estimate.IsError = true
			estimate.Error = "missing `multiplier_value` in input"
			return
		}

		inputBid, ok = multiplierValue.(float64)
		if !ok {
			estimate.IsError = true
			estimate.Error = "invalid `multiplier_value` in input"
			return
		}
	}

	estimate.InputBid = inputBid

	var appliedRules []string

	for _, item := range r.factorRuleItems {
		if item.Resource != resource {
			continue
		}
		if len(item.Dimension) == 0 {
			oldBid := inputBid
			inputBid = inputBid * item.Factor
			appliedRules = append(appliedRules, fmt.Sprintf("Applied global factor rule: %.2f, changed bid from %.2f to %.2f",
				item.Factor, oldBid, inputBid))
			continue
		}

		isMatch := true
		var dimMatches []string
		for _, dim := range item.Dimension {
			inpVal, inpOk := estimate.Input[dim.Name]
			enrichVal, enrichOk := estimate.EnrichedData[dim.Name]
			if !inpOk && !enrichOk {
				estimate.IsError = true
				estimate.Error = fmt.Sprintf("missing `%s` in input", dim.Name)
				return
			}

			// TODO: maybe convert value based on schema
			var dimVal string
			var ok bool

			if inpOk {
				dimVal, ok = inpVal.(string)
			} else {
				dimVal, ok = enrichVal.(string)
			}
			if !ok {
				estimate.IsError = true
				estimate.Error = fmt.Sprintf("invalid `%s` in input", dim.Name)
				return
			}

			if dim.Value != dimVal {
				isMatch = false
				break
			}
			dimMatches = append(dimMatches, fmt.Sprintf("%s=%s", dim.Name, dim.Value))
		}

		if isMatch {
			oldBid := inputBid
			inputBid = inputBid * item.Factor
			appliedRules = append(appliedRules, fmt.Sprintf("Applied factor rule: %.2f for dimensions [%s], changed bid from %.2f to %.2f",
				item.Factor,
				strings.Join(dimMatches, ", "),
				oldBid,
				inputBid))
			break
		}
	}

	for _, item := range r.minmaxRuleItems {
		if item.Resource != resource {
			continue
		}
		oldBid := inputBid
		if inputBid < item.Minimum {
			inputBid = item.Minimum
			appliedRules = append(appliedRules, fmt.Sprintf("Applied minimum rule: %.2f, changed bid from %.2f to %.2f",
				item.Minimum, oldBid, inputBid))
			break
		}
		if inputBid > item.Maximum {
			inputBid = item.Maximum
			appliedRules = append(appliedRules, fmt.Sprintf("Applied maximum rule: %.2f, changed bid from %.2f to %.2f",
				item.Maximum, oldBid, inputBid))
			break
		}
	}

	if len(appliedRules) > 0 {
		estimate.ChangeReason = strings.Join(appliedRules, "; ")
	} else {
		estimate.ChangeReason = "No rules applied"
	}

	estimate.OutputBid = inputBid
}

func (r *gha) RuleName() string {
	return r.name
}

func (r *gha) SupportedMarketingChannels() collections.Set[string] {
	return r.marketingChannels
}

func init() {
	registerRule("gha", newGha)
}

func (g *gha) splitAndSortRules(ruleItems []common.RuleItem) error {
	var factorRules []ruleItem
	var minmaxRules []ruleItem

	for _, item := range ruleItems {
		var info ruleItem
		if err := json.Unmarshal([]byte(item.Info), &info); err != nil {
			return fmt.Errorf("failed to unmarshal rule info: %w", err)
		}

		if info.Operator == "factor" {
			factorRules = append(factorRules, info)
			continue
		}
		minmaxRules = append(minmaxRules, info)
	}

	// Sort all factor rules by priority
	sort.Slice(factorRules, func(i, j int) bool {
		return factorRules[i].Priority < factorRules[j].Priority
	})
	// Sort all min-max rules by priority
	sort.Slice(minmaxRules, func(i, j int) bool {
		return minmaxRules[i].Priority < minmaxRules[j].Priority
	})
	g.factorRuleItems = factorRules
	g.minmaxRuleItems = minmaxRules
	return nil
}
