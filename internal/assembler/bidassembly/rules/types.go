package rules

type ruleItem struct {
	Dimension []dimension `json:"dimensions,omitempty"`
	Resource  string      `json:"resource"`
	Operator  string      `json:"operator"`
	Factor    float64     `json:"factor,omitempty"`
	Minimum   float64     `json:"minimum,omitempty"`
	Maximum   float64     `json:"maximum,omitempty"`
	Priority  int         `json:"priority"`
}

type dimension struct {
	Name string `json:"dimension"`
	// TODO: change this to interface?
	Value string `json:"dimension_value"`
}
