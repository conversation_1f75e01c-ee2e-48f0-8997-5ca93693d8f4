package filters

import (
	"database/sql"
	"fmt"
	"testing"

	_ "github.com/marcboeker/go-duckdb/v2"
	"github.com/rs/zerolog"
	"github.expedia.biz/gmo-performance-marketing/loki/internal/assembler/common"
	"github.expedia.biz/gmo-performance-marketing/sem-lib-go/pkg/collections"
)

// This file serves as a guide for onboarding new filters.
// It provides a dummy implementation and a test case to illustrate how a new
// filter can be tested in isolation without running the full assembly job.

// --- Dummy Filter ---

// 1. Define a struct for your new filter.
type dummyFilter struct {
	logger zerolog.Logger
	// Add any fields your filter needs.
}

// 2. Implement the filters.Interface.

func (d *dummyFilter) LoadData(db *sql.DB) error {
	d.logger.Info().Msg("Loading data for dummy filter")
	// Load data required for filtering.
	_, err := db.Exec("CREATE TABLE dummy_filter_data (id_to_filter VARCHAR);")
	if err != nil {
		return fmt.Errorf("failed to create dummy filter table: %w", err)
	}
	_, err = db.Exec("INSERT INTO dummy_filter_data VALUES ('2');")
	return err
}

func (d *dummyFilter) FilterData(db *sql.DB, estimatesTable string, bidUnitConfig common.BidUnitConfig) error {
	d.logger.Info().Msgf("Filtering data in table %s for bid unit config %s", estimatesTable, bidUnitConfig.Id)
	// Write the SQL to filter rows from the estimates table.
	// This example would delete rows where the 'id' column matches an id in the filter table.
	query := fmt.Sprintf("DELETE FROM %s WHERE id IN (SELECT id_to_filter FROM dummy_filter_data);", estimatesTable)
	_, err := db.Exec(query)
	return err
}

func (d *dummyFilter) SupportedMarketingChannels() collections.Set[string] {
	return collections.NewSet("DUMMY_CHANNEL")
}

func (d *dummyFilter) TableName() string {
	return "dummy_filter_data"
}

// 3. Create a constructor for your filter.
func newDummyFilter(logger zerolog.Logger) Interface {
	return &dummyFilter{logger: logger}
}

// --- Test Case ---

func setupFilterTest(t *testing.T) (*sql.DB, func()) {
	t.Helper()
	db, err := sql.Open("duckdb", "") // In-memory DuckDB
	if err != nil {
		t.Fatalf("Failed to open database: %v", err)
	}

	// Create a dummy estimates table to work with
	_, err = db.Exec("CREATE TABLE estimates (id VARCHAR, bid_price FLOAT);")
	if err != nil {
		t.Fatalf("Failed to create estimates table: %v", err)
	}
	_, err = db.Exec("INSERT INTO estimates VALUES ('1', 1.0), ('2', 2.0), ('3', 3.0);")
	if err != nil {
		t.Fatalf("Failed to insert into estimates table: %v", err)
	}

	tearDown := func() {
		_, err := db.Exec("DROP TABLE estimates;")
		if err != nil {
			t.Fatalf("Failed to drop estimates table: %v", err)
		}
		db.Close()
	}

	return db, tearDown
}

func TestFilterOnboarding(t *testing.T) {
	l := zerolog.Nop()
	db, tearDown := setupFilterTest(t)
	defer tearDown()

	// --- Filter Onboarding ---
	// Use the constructor for your new filter here.
	filter := newDummyFilter(l)

	// 1. Test LoadData
	err := filter.LoadData(db)
	if err != nil {
		t.Fatalf("Filter failed to load data: %v", err)
	}
	defer db.Exec(fmt.Sprintf("DROP TABLE %s;", filter.TableName()))

	// 2. Test FilterData
	err = filter.FilterData(db, "estimates", common.BidUnitConfig{})
	if err != nil {
		t.Fatalf("Filter failed to filter data: %v", err)
	}

	// 3. Verify results
	var count int
	err = db.QueryRow("SELECT COUNT(*) FROM estimates;").Scan(&count)
	if err != nil {
		t.Fatalf("Failed to query estimates table: %v", err)
	}

	if count != 2 {
		t.Errorf("Expected 2 rows after filtering, but got %d", count)
	}

	err = db.QueryRow("SELECT COUNT(*) FROM estimates WHERE id = '2';").Scan(&count)
	if err != nil {
		t.Fatalf("Failed to query estimates table: %v", err)
	}
	if count > 0 {
		t.Errorf("Row with id '2' was not filtered out")
	}
}
