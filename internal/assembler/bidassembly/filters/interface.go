package filters

import (
	"database/sql"
	"fmt"
	"reflect"

	"github.com/rs/zerolog"
	"github.expedia.biz/gmo-performance-marketing/loki/internal/assembler/common"
	pb "github.expedia.biz/gmo-performance-marketing/pb-go/pkg/loki/v1"
	"github.expedia.biz/gmo-performance-marketing/sem-lib-go/pkg/collections"
)

type Interface interface {
	LoadData(db *sql.DB) error
	FilterData(db *sql.DB, estimatesTable string, bidUnitConfig common.BidUnitConfig) error
	SupportedMarketingChannels() collections.Set[string]
	TableName() string
}

func GetFilter(config *pb.PipelineAssemblerConfig, s3Utils *common.S3Utils, logger zerolog.Logger, runParameters map[string]interface{}) (Interface, error) {

	switch config.Filter.(type) {
	case *pb.PipelineAssemblerConfig_PropertyInventoryFilter:
		return newPropertyInventoryFilter(s3Utils, logger, config, runParameters)
	default:
		return nil, fmt.Errorf("unsupported filter type: %v", reflect.TypeOf(config.Filter))
	}
}
