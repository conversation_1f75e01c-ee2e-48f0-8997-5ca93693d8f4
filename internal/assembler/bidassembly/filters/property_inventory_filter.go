package filters

import (
	"context"
	"database/sql"
	"fmt"
	"os"
	"strings"

	"github.com/Netflix/go-env"
	"github.com/rs/zerolog"
	"github.expedia.biz/gmo-performance-marketing/loki/internal/assembler/common"
	pb "github.expedia.biz/gmo-performance-marketing/pb-go/pkg/loki/v1"
	"github.expedia.biz/gmo-performance-marketing/sem-lib-go/pkg/collections"
)

type config struct {
	InventoryPath string `env:"INVENTORY_S3_PATH,default=s3://eg-marketing-platform-test/loki/meta-inventory/hotels/latest/"`
}

type PropertyInventoryFilter struct {
	filterConfig           config
	s3Utils                *common.S3Utils
	logger                 zerolog.Logger
	piplineAssemblerConfig *pb.PipelineAssemblerConfig
	runParameters          map[string]interface{}
}

func newPropertyInventoryFilter(s3Utils *common.S3Utils, logger zerolog.Logger, inventoryFilterConfig *pb.PipelineAssemblerConfig, runParameters map[string]interface{}) (*PropertyInventoryFilter, error) {
	var filterConfig config

	_, err := env.UnmarshalFromEnviron(&filterConfig)
	if err != nil {
		return nil, fmt.Errorf("failed to unmarshal property filter config: %w", err)
	}
	l := logger.With().Str("component", "propertyInventoryFilter").Logger()

	return &PropertyInventoryFilter{filterConfig: filterConfig, s3Utils: s3Utils, logger: l, piplineAssemblerConfig: inventoryFilterConfig, runParameters: runParameters}, nil
}

func (p *PropertyInventoryFilter) LoadData(db *sql.DB) error {
	_ = os.MkdirAll(p.localPath(), os.ModePerm)
	p.logger.Info().Msgf("downloading property inventory files from %s", p.filterConfig.InventoryPath)
	s3Objects, err := p.s3Utils.ListFiles(p.filterConfig.InventoryPath)
	if err != nil {
		return fmt.Errorf("s3List: %w", err)
	}
	err = p.s3Utils.DownloadFiles(p.localPath(), s3Objects)
	if err != nil {
		return fmt.Errorf("s3Download: %w", err)
	}
	p.logger.Info().Msgf("successfully downloaded %d inventory files", len(s3Objects))

	query := fmt.Sprintf("CREATE OR REPLACE TABLE %s AS (SELECT * FROM read_parquet('%s/*.parquet', union_by_name = true))", p.TableName(), p.localPath())
	p.logger.Info().Msgf("Running query: %s", query)
	result, err := db.ExecContext(context.Background(), query)
	if err != nil {
		return fmt.Errorf("loadPropertyInventoryData: %w", err)
	}

	rowsAffected, _ := result.RowsAffected()
	p.logger.Info().Msgf("Total property inventory rows loaded: %d", rowsAffected)
	return nil
}

func (p *PropertyInventoryFilter) FilterData(db *sql.DB, estimatesTable string, bidUnitConfig common.BidUnitConfig) error {
	l := p.logger.With().Str("bidUnitConfig", bidUnitConfig.Id).Logger()
	filterType := p.piplineAssemblerConfig.GetPropertyInventoryFilter().Type
	brand := strings.ToLower(p.runParameters["brand"].(string))
	if brand != "hcom" && brand != "vrbo" {
		brand = "bexg"
	}
	partner := strings.ToLower(p.runParameters["partner"].(string))

	var condition string
	switch filterType {
	case pb.PropertyInventoryType_PROPERTY_INVENTORY_TYPE_ALL:
		condition = ""
	case pb.PropertyInventoryType_PROPERTY_INVENTORY_TYPE_BIDDABLE:
		condition = fmt.Sprintf("WHERE biddable.%s.%s.status = True", partner, brand)
	case pb.PropertyInventoryType_PROPERTY_INVENTORY_TYPE_SELLABLE:
		condition = fmt.Sprintf("WHERE sellable.%s.status = True", brand)
	case pb.PropertyInventoryType_PROPERTY_INVENTORY_TYPE_MAPPED:
		condition = fmt.Sprintf("WHERE biddable.%s.%s.status = True AND mapped.%s.%s.status == True", partner, brand, partner, brand)
	default:
		return fmt.Errorf("unknown filter type: %s", filterType)
	}

	filterColumn := "hotel_id"
	selectQuery := fmt.Sprintf("SELECT %s FROM %s %s", "eg_property_id", p.TableName(), condition)
	updateQuery := fmt.Sprintf("UPDATE %s SET is_filtered = TRUE, filtered_reason = 'property doesn`t match inventory condition' WHERE (%s) not in (%s) AND is_filtered = FALSE AND bid_unit_type = 'base_bid'", estimatesTable, filterColumn, selectQuery)

	l.Info().Msgf("running query: %s", updateQuery)
	result, err := db.ExecContext(context.Background(), updateQuery)
	if err != nil {
		return fmt.Errorf("filterPropertyInventory: %w", err)
	}
	rowsAffected, _ := result.RowsAffected()
	l.Info().Msgf("Total rows updated: %d", rowsAffected)
	return nil
}

func (p *PropertyInventoryFilter) SupportedMarketingChannels() collections.Set[string] {
	supportedMarketingChannels := collections.Set[string]{}
	supportedMarketingChannels.AddAll(
		"google-hotel-ads", "google-search", "bing-search", "trip-advisor", "cheap-flights", "kayak", "trivago")
	return supportedMarketingChannels
}

func (p *PropertyInventoryFilter) TableName() string {
	return "property_inventory"
}

func (p *PropertyInventoryFilter) localPath() string {
	return "./workspace/property-inventory"
}
