# Filters

Filters are used to remove bids from the main estimates table based on certain criteria during the assembly process.

## Onboarding a New Filter

To add a new filter, follow these steps:

1.  **Implement the Interface**: Create a new `.go` file in this directory for your filter. In that file, define a struct and implement the `filters.Interface`.
    *   `LoadData(*sql.DB) error`: This method should handle loading any necessary data for the filter (e.g., a list of IDs to exclude) and preparing it in the provided in-memory DuckDB database.
    *   `FilterData(*sql.DB, string, common.BidUnitConfig) error`: This method contains the core filtering logic. It should execute a SQL query (usually a `DELETE` statement) against the `estimatesTable` to remove rows that meet the filter's criteria.
    *   `SupportedMarketingChannels() collections.Set[string]`: Return a set of marketing channel strings that this filter supports.
    *   `TableName() string`: Return the name of the table this filter creates in the database.

2.  **Add to Protobuf**: Add your new filter to the `Filter` oneof field in the `loki.proto` file.

3.  **Register the Filter**: In `internal/assembler/bidassembly/filters/interface.go`, add a new case to the `switch` statement in the `GetFilter` function. This will instantiate your filter when it's specified in a pipeline configuration.

## Testing Locally

You can test your filter in isolation without running the entire bid assembly job. This allows for faster development and easier debugging.

The `onboarding_test.go` file provides a template for how to write a unit test for your filter.

To test your filter:

1.  Create a new test function in `onboarding_test.go` or a new `_test.go` file.
2.  Create an in-memory DuckDB instance.
3.  Create a dummy `estimates` table with sample data, including rows that you expect to be filtered out.
4.  Instantiate your filter using its constructor (e.g., `newMyFilter()`).
5.  Call the `LoadData` method and verify that the data was loaded correctly.
6.  Call the `FilterData` method.
7.  Query the `estimates` table and assert that the correct rows were deleted and the row count is as expected.

You can run the tests from the command line:

```bash
go test ./internal/assembler/bidassembly/filters/...
```
