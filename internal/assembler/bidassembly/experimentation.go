package bidassembly

import (
	"context"
	"database/sql"
	"fmt"
	"os"
	"strings"

	"github.com/rs/zerolog"
	"github.expedia.biz/gmo-performance-marketing/loki/internal/assembler/common"
	"github.expedia.biz/gmo-performance-marketing/loki/internal/assembler/config"
	"github.expedia.biz/gmo-performance-marketing/loki/internal/client"
	"github.expedia.biz/gmo-performance-marketing/sem-lib-go/pkg/collections"
)

var experimentUnitToColumns = map[string][]string{
	"GOOGLE-PROPERTY": {"hotel_id"},
}

type experimentation struct {
	marketlabClient   client.MarketLabClient
	bidAssemblyConfig *config.BidAssemblyConfig
	s3Utils           *common.S3Utils
	db                *sql.DB
	logger            zerolog.Logger

	experimentToJoinColumns map[string][]string
}

func newExperimentation(marketlabClient client.MarketLabClient, bidAssemblyConfig *config.BidAssemblyConfig, s3Utils *common.S3Utils, db *sql.DB, logger zerolog.Logger) *experimentation {
	logger = logger.With().Str("component", "experimentation").Logger()
	return &experimentation{marketlabClient: marketlabClient, bidAssemblyConfig: bidAssemblyConfig, s3Utils: s3Utils, db: db, logger: logger, experimentToJoinColumns: make(map[string][]string)}
}

// load loads experiment data for each experiment in the provided set
// It retrieves experiments from the MarketLab service and processes them.
func (e *experimentation) load(experiments collections.Set[string]) error {
	for experimentName := range experiments {
		// Retrieve experiment details using MarketLab API
		experiment, err := e.marketlabClient.GetMarketLabExperimentByName(experimentName, e.bidAssemblyConfig.MarketLabSvcAddr)
		if err != nil {
			return fmt.Errorf("getExperimentInfo: %w", err)
		}

		// Create a unique unit identifier and check if it exists in the predefined map
		unit := fmt.Sprintf("%s-%s", experiment.Partner, experiment.BucketingUnit)
		joinColumns, ok := experimentUnitToColumns[unit]
		if !ok {
			return fmt.Errorf("experimentation not supported for bucketing unit: %s", unit)
		}

		// Store the join columns for the current experiment
		e.experimentToJoinColumns[experimentName] = joinColumns

		// Download and load the experiment data
		var buckets []client.Bucket
		for _, bucket := range experiment.Bucket {
			buckets = append(buckets, bucket)
		}

		// Handle downloading and processing the experiment data
		err = e.downloadAndLoadExperimentData(buckets, experimentName)
		if err != nil {
			return err
		}
	}
	return nil
}

// downloadAndLoadExperimentData downloads the experiment data for the given buckets and loads it into the database.
func (e *experimentation) downloadAndLoadExperimentData(buckets []client.Bucket, experimentName string) error {
	// Define the path where experiment data will be stored locally
	experimentWorkspace := fmt.Sprintf("%s/experiment_name=%s", e.experimentWorkspacePath(), experimentName)

	// Loop over each bucket to download the relevant files from S3
	for _, bucket := range buckets {
		bucketWorkspace := fmt.Sprintf("%s/bucket=%s", experimentWorkspace, bucket.Name)
		_ = os.MkdirAll(bucketWorkspace, os.ModePerm)

		// Download the file for the bucket and handle any errors
		_, err := e.s3Utils.DownloadFile(bucket.S3Path, bucketWorkspace)
		if err != nil {
			return err
		}
	}

	// Construct the query to create the table and load the CSV data
	query := fmt.Sprintf(
		`CREATE OR REPLACE TABLE %s AS 
		SELECT 
			*
		FROM read_csv('%s/**/*.gz', header=true, delim='\t', compression='gzip')`,
		e.tableName(experimentName), experimentWorkspace)

	// Log the query and execute it
	e.logger.Info().Msgf("create table: %s", query)
	result, err := e.db.ExecContext(context.Background(), query)
	if err != nil {
		return fmt.Errorf("createExperimentsTable: %w", err)
	}

	// Log the number of rows affected by the query
	rowsAffected, _ := result.RowsAffected()
	e.logger.Info().Msgf("Experiment rows inserted: %d", rowsAffected)
	return nil
}

// TagEstimates tags estimates with experiment information based on the given BidUnitConfig.
func (e *experimentation) TagEstimates(estimatesTable string, bidUnitConfig common.BidUnitConfig) error {
	l := e.logger.With().Str("bidUnitConfigId", bidUnitConfig.Id).Logger()
	joinColumns, _ := e.experimentToJoinColumns[bidUnitConfig.ExperimentName]

	// Log the join columns for debugging
	l.Info().Msgf("splittingEstimates: %s", strings.Join(joinColumns, ","))

	// Sanitize inputs to prevent SQL injection
	sanitizedExperimentName := common.SanitizeSQLInput(bidUnitConfig.ExperimentName)
	sanitizedBinId := common.SanitizeSQLInput(bidUnitConfig.BinId)
	sanitizedEstimatesTable := common.SanitizeSQLInput(estimatesTable)

	// Select the necessary columns from the experimentation table based on the bucketing unit
	selectColumns := strings.Join(joinColumns, ",")
	experimentTable := e.tableName(sanitizedExperimentName)

	selectQuery := fmt.Sprintf("SELECT %s FROM %s WHERE bucket = '%s'",
		selectColumns, experimentTable, sanitizedBinId)

	// Construct the update query for the estimates table
	updateQuery := fmt.Sprintf("UPDATE %s SET experiment_name = '%s', experiment_bucket = '%s' WHERE (%s) in (%s) and is_filtered = FALSE",
		sanitizedEstimatesTable, sanitizedExperimentName, sanitizedBinId, selectColumns, selectQuery)

	// Log the update query for debugging
	l.Info().Msgf("running update: %s", updateQuery)

	// Execute the update query on the database
	result, err := e.db.ExecContext(context.Background(), updateQuery)
	if err != nil {
		return fmt.Errorf("updateEstimatesTable: %w", err)
	}

	// Log the number of rows affected by the update
	rowsAffected, _ := result.RowsAffected()
	l.Info().Msgf("rows affected: %d", rowsAffected)
	return nil
}

func (e *experimentation) experimentWorkspacePath() string {
	return "./workspace/experiments"
}

func (e *experimentation) tableName(experimentName string) string {
	return "experiments_" + experimentName
}
