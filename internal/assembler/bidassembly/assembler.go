package bidassembly

import (
	"compress/gzip"
	"context"
	"database/sql"
	"encoding/csv"
	"encoding/json"
	"fmt"
	"io"
	"net/url"
	"os"
	"sync"

	"github.com/rs/zerolog"
	"github.expedia.biz/gmo-performance-marketing/loki/internal/assembler/bidassembly/enrichers"
	"github.expedia.biz/gmo-performance-marketing/loki/internal/assembler/bidassembly/filters"
	"github.expedia.biz/gmo-performance-marketing/loki/internal/assembler/bidassembly/rules"
	"github.expedia.biz/gmo-performance-marketing/loki/internal/assembler/common"
	"github.expedia.biz/gmo-performance-marketing/loki/internal/assembler/config"
	"github.expedia.biz/gmo-performance-marketing/sem-lib-go/pkg/collections"
)

const (
	estimatesStage = "estimates"
)

var experimentColumns = collections.Set[string]{"experiment_name": {}, "experiment_bucket": {}}

var aliases = map[string]string{
	"accountId":      "account_id",
	"AccountId":      "account_id",
	"CampaignId":     "campaign_id",
	"campaignId":     "campaign_id",
	"ad_group_id":    "ad_group_id",
	"adGroupId":      "ad_group_id",
	"eg_property_id": "hotel_id",
}

type assemblerStats struct {
	errorBids           int64
	logsS3Path          string
	assembledBidsS3Path string
	isError             bool
	errorMessage        string
}

type assembler struct {
	assemblerStats

	s3Utils           *common.S3Utils
	logger            zerolog.Logger
	bidAssemblyConfig *config.BidAssemblyConfig
	db                *sql.DB
	filter            filters.Interface
	experimentation   *experimentation
	enrichers         []enrichers.Interface

	bidUnitConfig       common.BidUnitConfig
	logsFile            *os.File
	assembledBidsFile   *os.File
	logsFileWriter      *csv.Writer
	assembledBidsWriter *csv.Writer
	headers             []string
}

func newAssembler(
	s3Utils *common.S3Utils,
	logger zerolog.Logger,
	bidAssemblyConfig *config.BidAssemblyConfig,
	db *sql.DB,
	filter filters.Interface,
	experimentation *experimentation,
	enrichers []enrichers.Interface,
	bidUnitConfig common.BidUnitConfig,
) *assembler {
	l := logger.With().Str("component", "assembler").Str("bidUnitConfigId", bidUnitConfig.Id).Logger()
	return &assembler{
		s3Utils:           s3Utils,
		logger:            l,
		bidAssemblyConfig: bidAssemblyConfig,
		db:                db,
		filter:            filter,
		experimentation:   experimentation,
		enrichers:         enrichers,
		bidUnitConfig:     bidUnitConfig,
	}
}

// run handles the main flow of the assembler process, including initialization,
// downloading estimates, applying filters and enrichers, tagging estimates, and
// managing rules.
func (a *assembler) run() assemblerStats {

	_ = os.MkdirAll(a.localWorkspacePath(), os.ModePerm)
	// Initialize file writers for logs and pre-entities
	err := a.initializeFileWriters()
	if err != nil {
		a.handleError(err, "intializeFileWriters")
		return a.assemblerStats
	}

	// Download estimates from a given path
	err = a.downloadEstimates()
	if err != nil {
		a.handleError(err, "downloadEstimates")
		return a.assemblerStats
	}

	// Load estimates into the database
	err = a.loadEstimates()
	if err != nil {
		a.handleError(err, "loadEstimates")
		return a.assemblerStats
	}

	estimatesTable := a.tableName()
	// Apply data filter if filter is set
	if a.filter != nil {
		err = a.filter.FilterData(a.db, estimatesTable, a.bidUnitConfig)
		if err != nil {
			a.handleError(err, "applyFilter")
			return a.assemblerStats
		}
	}

	// Enrich data using each enricher in the enrichers list
	if len(a.enrichers) > 0 {
		for _, enricher := range a.enrichers {
			err = enricher.EnrichData(a.db, estimatesTable, a.bidUnitConfig)
			if err != nil {
				a.handleError(err, "enrichData")
				return a.assemblerStats
			}
		}
	}

	// Apply experimentation if experiment name is set
	if a.bidUnitConfig.ExperimentName != "" {
		err = a.experimentation.TagEstimates(estimatesTable, a.bidUnitConfig)
		if err != nil {
			a.handleError(err, "tagEstimates")
			return a.assemblerStats
		}
	}

	// Get the implemented rules based on configuration
	implementedRules, err := rules.GetImplementedRules(a.bidUnitConfig.Rules, a.logger)
	if err != nil {
		a.handleError(err, "getImplementedRules")
		return a.assemblerStats
	}

	// Process the implemented rules
	// TODO: what if no rules but want to apply experimentation
	for _, rule := range a.bidUnitConfig.Rules {
		outChan := make(chan *common.Estimate, 10_000)
		errChan := make(chan error, 1)

		var ruleWg sync.WaitGroup
		ruleWg.Add(2)
		// Start concurrent processing of bids
		go a.applyRule(&ruleWg, errChan, outChan, implementedRules[rule.RuleSchema])
		go a.pollOutChan(outChan, &ruleWg)
		ruleWg.Wait()

		if len(errChan) > 0 {
			err = <-errChan
			a.handleError(err, "applyRule")
			return a.assemblerStats
		}
	}

	// Close resources and upload logs
	a.close()
	a.uploadLogs()

	return a.assemblerStats
}

// close ensures proper closure of file writers and other resources.
func (a *assembler) close() {
	// Flush writers before closing files
	if a.assembledBidsWriter != nil {
		a.assembledBidsWriter.Flush()
	}
	if a.logsFileWriter != nil {
		a.logsFileWriter.Flush()
	}

	// Close files
	if a.logsFile != nil {
		a.logsFile.Close()
	}
	if a.assembledBidsFile != nil {
		a.assembledBidsFile.Close() // Fixed: was incorrectly closing a.logsFile again
	}
}

// uploadLogs uploads logs to an S3 path if no errors occurred during the assembly.
func (a *assembler) uploadLogs() {
	if a.assemblerStats.isError {
		a.logger.Info().Msgf("skipping upload as there was error during assembly...")
		return
	}

	// Compress CSV files to gzip format before uploading
	if err := a.compressCSVFiles(); err != nil {
		a.handleError(err, "compressCSVFiles")
		return
	}

	s3Path := fmt.Sprintf("%s/assembler/runId=%s/bidUnitConfigId=%s/", a.bidAssemblyConfig.LokiS3Path, a.bidUnitConfig.AssemberRunId, a.bidUnitConfig.Id)
	err := a.s3Utils.UploadDir(a.localWorkspacePath(), s3Path)
	if err != nil {
		a.handleError(err, "uploadDir")
	} else {
		a.assemblerStats.logsS3Path = fmt.Sprintf("%slogs.csv.gz", s3Path)
		a.assemblerStats.assembledBidsS3Path = fmt.Sprintf("%sassembled_bids.csv.gz", s3Path)
	}
}

// initializeFileWriters initializes the file writers for logs and pre-entities.
func (a *assembler) initializeFileWriters() error {
	logsFilePath := fmt.Sprintf("%s/logs.csv", a.localWorkspacePath())
	assembledBidsPath := fmt.Sprintf("%s/assembled_bids.csv", a.localWorkspacePath())

	var err error
	a.logsFile, err = a.openFile(logsFilePath, os.O_APPEND|os.O_CREATE|os.O_WRONLY)
	if err != nil {
		return err
	}

	a.assembledBidsFile, err = a.openFile(assembledBidsPath, os.O_APPEND|os.O_CREATE|os.O_WRONLY)
	if err != nil {
		return err
	}

	a.logsFileWriter = csv.NewWriter(a.logsFile)
	a.assembledBidsWriter = csv.NewWriter(a.assembledBidsFile)
	return nil
}

func (a *assembler) pollOutChan(outChan <-chan *common.Estimate, wg *sync.WaitGroup) {
	defer wg.Done()

	for estimate := range outChan {
		if len(a.headers) == 0 {
			a.headers = estimate.GetHeaders()
			a.logsFileWriter.Write(a.headers)
			a.assembledBidsWriter.Write(a.headers)
		}
		if estimate.IsSkipped {
			continue
		}

		values := estimate.GetValues(a.headers)
		a.logsFileWriter.Write(values)

		if estimate.Input["is_filtered"].(bool) {
			continue
		}
		if estimate.IsError {
			a.assemblerStats.errorBids += 1
			continue
		}
		a.assembledBidsWriter.Write(values)
	}
}

func (a *assembler) openFile(path string, flag int) (*os.File, error) {
	file, err := os.OpenFile(path, flag, os.ModePerm)
	if err != nil {
		a.logger.Warn().Msgf("openFile: %v", err)
		return nil, fmt.Errorf("openFile: %w", err)
	}
	return file, nil
}

// sendBids sends bids to the input channel for processing by the rules.
func (a *assembler) applyRule(wg *sync.WaitGroup, errChan chan<- error, outChan chan<- *common.Estimate, rule rules.Interface) {
	defer wg.Done()
	defer close(errChan)
	defer close(outChan)

	handleErr := func(err error) {
		errChan <- err
	}
	a.logger.Info().Msgf("fetching estimates for bid unit config: %s", a.bidUnitConfig.Id)
	query := a.getEstimatesQuery(a.bidUnitConfig)
	a.logger.Info().Msgf("running estimates query: %s", query)
	rows, err := a.db.QueryContext(context.Background(), query)
	if err != nil {
		handleErr(err)
		return
	}
	defer rows.Close()

	a.logger.Info().Msgf("sending estimates to inChan...")
	for rows.Next() {
		// Create a map to store the row data
		columns, err := rows.Columns()
		if err != nil {
			handleErr(err)
			return
		}

		// Prepare a slice of interfaces to hold the values of each column
		values := make([]interface{}, len(columns))
		for i := range values {
			values[i] = new(interface{})
		}

		// Scan the row data into the values slice
		if err := rows.Scan(values...); err != nil {
			handleErr(err)
			return
		}

		// Create a map to store the column name and corresponding value
		result := make(map[string]interface{})
		for i, column := range columns {
			// Cast the scanned value to the correct type (interface{})
			result[column] = *(values[i].(*interface{}))
		}

		enrichedData := make(map[string]interface{})
		if metadataStr, ok := result["assembler_metadata"].(string); ok && metadataStr != "" {
			err := json.Unmarshal([]byte(metadataStr), &enrichedData)
			if err != nil {
				handleErr(fmt.Errorf("failed to unmarshal assembler_metadata: %w", err))
				return
			}
		}

		estimate := &common.Estimate{
			Input:        result,
			EnrichedData: enrichedData,
		}

		if is_filtered, _ := result["is_filtered"]; is_filtered.(bool) {
			outChan <- estimate
			continue
		}
		rule.ApplyRule(estimate)
		outChan <- estimate
	}
}

// getEstimatesQuery generates the SQL query to retrieve estimates based on bid unit configuration.
func (a *assembler) getEstimatesQuery(bidUnitConfig common.BidUnitConfig) string {
	tableName := a.tableName()

	switch bidUnitConfig.Type {
	case common.CONTROL, common.CHALLENGER:
		// Sanitize inputs to prevent SQL injection
		experimentName := common.SanitizeSQLInput(bidUnitConfig.ExperimentName)
		binId := common.SanitizeSQLInput(bidUnitConfig.ExperimentConfig.BinId)
		return fmt.Sprintf("SELECT * FROM %s WHERE experiment_name = '%s' AND experiment_bucket = '%s'",
			tableName, experimentName, binId)
	default:
		// TODO select when experiment is null for BAU and OBSERVE usecase
		return fmt.Sprintf("SELECT * FROM %s", tableName)
	}
}

func (a *assembler) handleError(err error, context string) {
	a.logger.Error().Err(err).Msg(context)
	a.assemblerStats.isError = true
	a.assemblerStats.errorMessage = err.Error()
}

func (a *assembler) downloadEstimates() error {
	a.logger.Info().Msgf("dowloading estimates from : %s", a.bidUnitConfig.EstimatesPath)
	s3Objects, err := a.s3Utils.ListFiles(a.bidUnitConfig.EstimatesPath)
	if err != nil {
		return fmt.Errorf("s3List: %w", err)
	}

	decodedEstimatesPath, _ := url.QueryUnescape(a.bidUnitConfig.EstimatesPath)
	s3Object, _ := a.s3Utils.Parse(decodedEstimatesPath)
	localPath := fmt.Sprintf("%s/%s", a.inputsWorkspacePath(), *s3Object.Key)
	_ = os.MkdirAll(localPath, os.ModePerm)
	err = a.s3Utils.DownloadFiles(localPath, s3Objects)
	if err != nil {
		return fmt.Errorf("s3Download: %w", err)
	}
	return nil
}

func (a *assembler) loadEstimates() error {
	conn, err := a.db.Conn(context.Background())
	if err != nil {
		return fmt.Errorf("conn: %w", err)
	}
	defer conn.Close()

	query := fmt.Sprintf(
		`
			CREATE OR REPLACE TABLE %s AS 
				SELECT 
					*, 
					FALSE as is_filtered,
					'' AS filtered_reason,
					'%s' AS bid_unit_name,
					'{}' :: JSON AS assembler_metadata
				FROM read_parquet('%s/**/*.parquet', union_by_name = true)`,
		a.tableName(), a.bidUnitConfig.BidUnitName, a.inputsWorkspacePath(),
	)
	a.logger.Info().Msgf("ExecQuery: %s", query)
	result, err := conn.ExecContext(context.Background(), query)
	if err != nil {
		return fmt.Errorf("exec: %w", err)
	}
	totalRows, _ := result.RowsAffected()
	a.logger.Info().Msgf("inserted %d rows", totalRows)

	columnsQuery := fmt.Sprintf(
		`SELECT 
					column_name 
				FROM information_schema.columns 
				WHERE table_name = '%s'`, a.tableName())
	columnToAlias := map[string]string{}
	rows, err := conn.QueryContext(context.Background(), columnsQuery)
	if err != nil {
		return fmt.Errorf("queryColumns: %w", err)
	}
	defer rows.Close()

	experimentColumnsToAdd := experimentColumns
	for rows.Next() {
		var columnName string
		err = rows.Scan(&columnName)
		if err != nil {
			return fmt.Errorf("scan: %w", err)
		}
		if value, ok := aliases[columnName]; ok {
			a.logger.Info().Msgf("replacing column: %s with alias: %s", columnName, value)
			columnToAlias[columnName] = value
		}
		if experimentColumnsToAdd.Contains(columnName) {
			experimentColumnsToAdd.Remove(columnName)
		}
	}

	for columnName, alias := range columnToAlias {
		// Sanitize column names to prevent SQL injection
		sanitizedColumnName := common.SanitizeSQLInput(columnName)
		sanitizedAlias := common.SanitizeSQLInput(alias)

		alterQuery := fmt.Sprintf(`ALTER TABLE %s RENAME %s TO %s`,
			a.tableName(), sanitizedColumnName, sanitizedAlias)
		a.logger.Info().Msgf("Running query: %s", alterQuery)
		_, err := conn.ExecContext(context.Background(), alterQuery)
		if err != nil {
			return fmt.Errorf("exec: %w", err)
		}
		a.logger.Info().Msgf("altered column %s with alias %s", columnName, alias)
	}

	// add exp columns if not exists
	for column := range experimentColumnsToAdd {
		// Sanitize column name to prevent SQL injection
		sanitizedColumn := common.SanitizeSQLInput(column)

		alterQuery := fmt.Sprintf(`ALTER TABLE %s ADD COLUMN %s VARCHAR`,
			a.tableName(), sanitizedColumn)
		a.logger.Info().Msgf("Running query: %s", alterQuery)
		_, err := conn.ExecContext(context.Background(), alterQuery)
		if err != nil {
			return fmt.Errorf("exec: %w", err)
		}
		a.logger.Info().Msgf("added column %s", column)
	}
	return nil
}

func (a *assembler) localWorkspacePath() string {
	return fmt.Sprintf("%s/bidUnitConfigId=%s", a.bidAssemblyConfig.WorkDir, a.bidUnitConfig.Id)
}

func (a *assembler) tableName() string {
	return fmt.Sprintf("bidUnitConfig_%s_%s", a.bidUnitConfig.Id, estimatesStage)
}

func (a *assembler) inputsWorkspacePath() string {
	return fmt.Sprintf("%s/%s", a.localWorkspacePath(), estimatesStage)
}

func (a *assembler) compressCSVFiles() error {
	logsCSVPath := fmt.Sprintf("%s/logs.csv", a.localWorkspacePath())
	logsGzipPath := fmt.Sprintf("%s/logs.csv.gz", a.localWorkspacePath())

	if err := a.compressFile(logsCSVPath, logsGzipPath); err != nil {
		return fmt.Errorf("failed to compress logs.csv: %w", err)
	}

	assembledBidsCSVPath := fmt.Sprintf("%s/assembled_bids.csv", a.localWorkspacePath())
	assembledBidsGzipPath := fmt.Sprintf("%s/assembled_bids.csv.gz", a.localWorkspacePath())

	if err := a.compressFile(assembledBidsCSVPath, assembledBidsGzipPath); err != nil {
		return fmt.Errorf("failed to compress assembled_bids.csv: %w", err)
	}

	if err := os.Remove(logsCSVPath); err != nil {
		return fmt.Errorf("failed to remove original logs.csv: %w", err)
	}
	if err := os.Remove(assembledBidsCSVPath); err != nil {
		return fmt.Errorf("failed to remove original assembled_bids.csv: %w", err)
	}

	if err := a.cleanupDSStoreFiles(); err != nil {
		return fmt.Errorf("failed to cleanup .DS_Store files: %w", err)
	}

	a.logger.Info().Msg("removed original CSV files after compression")
	return nil
}

func (a *assembler) compressFile(sourcePath, destPath string) error {
	sourceFile, err := os.Open(sourcePath)
	if err != nil {
		return fmt.Errorf("failed to open source file %s: %w", sourcePath, err)
	}
	defer sourceFile.Close()

	destFile, err := os.Create(destPath)
	if err != nil {
		return fmt.Errorf("failed to create destination file %s: %w", destPath, err)
	}
	defer destFile.Close()

	gzipWriter := gzip.NewWriter(destFile)
	defer gzipWriter.Close()

	_, err = io.Copy(gzipWriter, sourceFile)
	if err != nil {
		return fmt.Errorf("failed to compress file: %w", err)
	}

	a.logger.Info().Msgf("compressed %s to %s", sourcePath, destPath)
	return nil
}

func (a *assembler) cleanupDSStoreFiles() error {
	workspacePath := a.localWorkspacePath()
	dsStorePath := fmt.Sprintf("%s/.DS_Store", workspacePath)

	if _, err := os.Stat(dsStorePath); err == nil {
		if err := os.Remove(dsStorePath); err != nil {
			return fmt.Errorf("failed to remove .DS_Store file: %w", err)
		}
		a.logger.Info().Msg("removed .DS_Store file")
	}

	return nil
}
