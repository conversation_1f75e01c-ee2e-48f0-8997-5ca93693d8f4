# Bid Assembly Job

The Bid Assembly job is responsible for assembling bids from various sources, enriching them, and applying rules.

## Configuration

The following environment variables are required to run the job:

| Variable | Description | Default Value                                                                   |
|---|---|---------------------------------------------------------------------------------|
| `LOKI_SERVICE_ADDR` | The address of the Loki service which hosts Pipeline, Bid Unit, Model, and Rule services. | `loki-server.test.marketing.expedia.com:443`                                    |
| `TRACKING_URL` | The URL for tracking workflows. | `https://argo-workflows.test.marketing.expedia.com/workflows/loki`              |
| `MARKETLAB_SERVICE_ADDR` | The address of the MarketLab service for experiment details. | `https://marketlab-ui.rcp.us-east-1.marketing.test.exp-aws.net/api/experiments` |
| `AWS_REGION` | The AWS region. | `us-west-2`                                                                     |
| `S3_PATH` | The base S3 path for Loki artifacts. | `s3://eg-marketing-platform-test/loki`                                          |
| `ENV` | The environment (e.g., DEV, PROD). | `DEV`                                                                           |
| `WORK_DIR` | The working directory for the job to store temporary files like the local database. | `./workspace`                                                                   |

## Process Flow

The job follows these steps to assemble bids:

1.  **Initialization**:
    *   Initializes a new job with its configuration.
    *   Initializes clients for Loki services (Assembler, Model, Pipeline, BidUnit, Rule) and MarketLab.

2.  **Fetch Core Entities**:
    *   Retrieves the `AssemblerRun` details.
    *   Fetches `PipelineRun` and `Pipeline` details to get the overall context and assembler configuration.
    *   Fetches all associated `ModelRun` details.

3.  **Prepare Bid Unit Configurations**:
    *   For each `ModelRun`, it fetches the corresponding `Model` and `BidUnit` details.
    *   It retrieves all `BidUnitConfig`s associated with the model and bid unit.
    *   For each `BidUnitConfig`, it fetches the associated `Rules`, `RuleItems`, and `RuleSchemas`.
    *   It constructs a list of `common.BidUnitConfig` objects containing all the necessary information for assembly.

4.  **Data Loading**:
    *   A local DuckDB instance is created.
    *   Based on the `PipelineAssemblerConfig`, it loads data for **filters** and **enrichers**. Filters are used to exclude certain bids, while enrichers add more data to the bids.

5.  **Experimentation**:
    *   If any `BidUnitConfig` is part of an experiment, it loads the experiment details from the MarketLab service.

6.  **Assembly Loop**:
    *   The job iterates through each prepared `BidUnitConfig`.
    *   For each config, it runs an `assembler` instance which performs the core logic:
        *   Reads bids from the S3 path specified in `EstimatesPath`.
        *   Applies filters.
        *   Applies enrichment.
        *   Applies rules.
        *   Handles experimentation logic.
        *   Writes the final assembled bids to a new S3 path.
    *   It collects statistics and any errors from the assembly process.

7.  **Update Run Status**:
    *   Finally, it updates the `AssemblerRun` with the outputs (including S3 paths to assembled bids and logs) and a list of all rule items that were applied.

## Scenarios Handled

The job is designed to handle several scenarios gracefully:

*   **Disabled Entities**: If a `BidUnit`, `BidUnitConfig`, or `RuleItem` is marked as `DISABLED`, it is skipped during the process, and a log message is recorded.
*   **No Filters**: If no filter is configured in the pipeline, the filtering step is skipped.
*   **Assembly Errors**: If an error occurs during the assembly for a specific `BidUnitConfig`, the error is caught, logged, and the `AssemblerRunOutput` for that config is marked with an error status and message. The job then proceeds to the next `BidUnitConfig`.
*   **Experimentation**: The job can handle bids that are part of A/B tests by fetching experiment details and applying specific logic based on the experiment bin.

## Cold Start Scenario

For the first-time setup or a cold start, the following manual steps are required to ensure the assembler job can run correctly. This is particularly useful when the full pipeline is not yet operational, or you need to test the assembler in isolation.

> **Warning**
> All entity creation and modification (steps 1-3) should be done against a local or test Loki server. **Never perform these setup steps in the production environment.** You can, however, use S3 paths pointing to production data for realistic local testing.

1.  **Set Up Local/Test Server and Create Core Entities**:
    *   Run the Loki server locally using `make serve`.
    *   Ensure all necessary **Bid Units** are created in your local/test service.
    *   Define and create the required **Rule Schemas**, **Rules**, and **Rule Items**.
    *   Create **Bid Unit Configs** to link the Bid Units with their corresponding Rules.

2.  **Set Up Pipeline**:
    *   Create a **Pipeline** with its status set to `DISABLED`. This prevents the pipeline controller from running it automatically.
    *   Add a dummy model configuration if the actual model is not ready.
    *   Add a dummy assembler configuration, but ensure it includes the `run_parameters` required by the assembler job for local testing.

3.  **Create Run Instances**:
    *   Manually create a **Pipeline Run** for the disabled pipeline. This will generate a `pipeline_run_id`.
    *   Manually create a **Model Run** associated with the `pipeline_run_id`. This will generate a `model_run_id`.
    *   In the Model Run, register the outputs for the specific Bid Units you created configs for. This includes pointing to the S3 path where the model's output bids are located.
    *   Manually create an **Assembler Run** associated with the `pipeline_run_id`.
    *   Manually run UpdatePipelineRun gRPC method to update the Pipeline Run with the `model_run_id` that you've just created above.

4.  **Data Preparation**:
    *   It is highly recommended to copy production data to your test environment for a realistic test. This includes:
        *   The model run output files (e.g., bids data) from S3.
        *   Any data required by filters and enrichers used in the assembler run.

With these steps completed, the assembler run is set up for a cold start case, and you can proceed with local testing.
