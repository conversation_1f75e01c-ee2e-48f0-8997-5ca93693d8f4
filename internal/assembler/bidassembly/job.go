package bidassembly

import (
	"database/sql"
	"encoding/json"
	"fmt"
	"os"

	_ "github.com/marcboeker/go-duckdb/v2"
	"github.com/rs/zerolog"
	"github.expedia.biz/gmo-performance-marketing/loki/internal/assembler/bidassembly/enrichers"
	"github.expedia.biz/gmo-performance-marketing/loki/internal/assembler/bidassembly/filters"
	"github.expedia.biz/gmo-performance-marketing/loki/internal/assembler/common"
	"github.expedia.biz/gmo-performance-marketing/loki/internal/assembler/config"
	"github.expedia.biz/gmo-performance-marketing/loki/internal/client"
	pbenums "github.expedia.biz/gmo-performance-marketing/pb-go/pkg/enums/v1"
	pb "github.expedia.biz/gmo-performance-marketing/pb-go/pkg/loki/v1"
	"github.expedia.biz/gmo-performance-marketing/sem-lib-go/pkg/aws"
	"github.expedia.biz/gmo-performance-marketing/sem-lib-go/pkg/collections"
)

type Job struct {
	s3Utils         *common.S3Utils
	modelClient     client.ModelClient
	pipelineClient  client.PipeLineClient
	marketlabClient client.MarketLabClient
	bidUnitClient   client.BidUnitClient
	ruleClient      client.RuleClient
	AssemblerClient client.AssemblerClient

	l             zerolog.Logger
	cfg           *config.BidAssemblyConfig
	runId         string
	workflowId    string
	runParameters map[string]interface{}
}

// NewJob initializes a new Job instance.
func NewJob(l zerolog.Logger, cfg *config.BidAssemblyConfig, runId string, workflowId string, overrideRunParameters *string) (*Job, error) {
	j := &Job{l: l, cfg: cfg, runId: runId, workflowId: workflowId}

	if overrideRunParameters != nil && *overrideRunParameters != "" {
		err := json.Unmarshal([]byte(*overrideRunParameters), &j.runParameters)
		if err != nil {
			return nil, err
		}
	}
	return j, nil
}

// Init initializes the various clients for the Job.
func (j *Job) Init() error {
	// Create assembler client
	var err error
	j.AssemblerClient, err = client.NewAssemblerClient(j.cfg.LokiSvcAddr, j.l)
	if err != nil {
		return fmt.Errorf("init assembler client: %w", err)
	}

	// get model svc client
	j.modelClient, err = client.GetNewModelClient(j.cfg.LokiSvcAddr, j.l)
	if err != nil {
		return fmt.Errorf("init model client: %w", err)
	}

	// get pipeline service client
	j.pipelineClient, err = client.GetNewPipelineClient(j.l, j.cfg.LokiSvcAddr)
	if err != nil {
		return fmt.Errorf("init pipeline service client: %w", err)
	}

	awsClient, err := aws.NewClient(j.cfg.AwsRegion)
	if err != nil {
		return fmt.Errorf("new aws client: %w", err)
	}

	j.s3Utils = common.NewS3Utils(j.l, awsClient)
	j.marketlabClient = client.GetMarketLabClient(j.l)

	j.bidUnitClient, err = client.NewBidUnitClient(j.l, j.cfg.LokiSvcAddr)
	if err != nil {
		return fmt.Errorf("init bid unit client: %w", err)
	}
	j.ruleClient, err = client.GetNewRuleClient(j.l, j.cfg.LokiSvcAddr)
	if err != nil {
		return fmt.Errorf("init rule client: %w", err)
	}

	return nil
}

func (j *Job) Run() error {

	assemblerRun, err := j.AssemblerClient.GetAssemblerRun(j.runId)
	if err != nil {
		return err
	}
	// Fetch pipeline run details
	pipelineRun, err := j.pipelineClient.GetPipelineRun(assemblerRun.PipelineRunId)
	if err != nil {
		return err
	}

	pipeline, err := j.pipelineClient.GetPipeline(pipelineRun.PipelineId)
	if err != nil {
		return err
	}

	// Fetch model runs
	modelRunIds := extractModelRunIds(pipelineRun.ModelRuns)
	modelRuns, err := j.modelClient.GetModelRuns(modelRunIds)
	if err != nil {
		return fmt.Errorf("get model runs: %w", err)
	}
	bidUnitConfigs, err := j.preparebidUnitConfigs(modelRuns, pipeline.MarketingChannel)
	if err != nil {
		return fmt.Errorf("prepare bid unit configs: %w", err)
	}

	// Ensure workspace directory exists before opening database
	if err := os.MkdirAll(j.cfg.WorkDir, os.ModePerm); err != nil {
		// Check if the error is because the directory already exists
		if !os.IsExist(err) {
			return fmt.Errorf("create workspace directory: %w", err)
		}
		// If directory already exists, that's fine - continue
	}

	db, err := sql.Open("duckdb", fmt.Sprintf("%s/bids.db", j.cfg.WorkDir))
	if err != nil {
		return fmt.Errorf("open db: %w", err)
	}
	defer db.Close()

	pipelineAssemblerConfig := pipeline.AssemblerConfig

	if len(j.runParameters) == 0 {
		err = json.Unmarshal([]byte(pipelineAssemblerConfig.RunParameters), &j.runParameters)
	}
	if err != nil {
		return fmt.Errorf("unmarshal run parameters: %w", err)
	}
	filter, err := j.getAndLoadFilterData(db, pipelineAssemblerConfig, j.runParameters)
	if err != nil {
		return fmt.Errorf("getAndLoadFilterData: %w", err)
	}

	enrichers, err := j.getAndLoadEnrichers(db, pipelineAssemblerConfig, j.runParameters)
	if err != nil {
		return fmt.Errorf("getAndLoadEnrichers: %w", err)
	}

	uniqueExperiments := collections.Set[string]{}
	for _, bidUnitConfig := range bidUnitConfigs {
		if bidUnitConfig.ExperimentName == "" {
			continue
		}
		uniqueExperiments.Add(bidUnitConfig.ExperimentName)
	}

	experimentation := newExperimentation(j.marketlabClient, j.cfg, j.s3Utils, db, j.l)
	err = experimentation.load(uniqueExperiments)
	if err != nil {
		return fmt.Errorf("loadExperiments: %w", err)
	}

	var assemblerRuleItems []*pb.AssemblerRuleItem
	var assemblerRunOutputs []*pb.AssemblerRunOutput

	for _, bidUnitConfig := range bidUnitConfigs {
		j.l.Info().Msgf("running for bid unit config %s", bidUnitConfig.Id)
		assembler := newAssembler(j.s3Utils, j.l, j.cfg, db, filter, experimentation, enrichers, bidUnitConfig)
		stats := assembler.run()
		for _, rule := range bidUnitConfig.Rules {
			for _, ruleItem := range rule.RuleItems {
				assemblerRuleItems = append(assemblerRuleItems, &pb.AssemblerRuleItem{RuleItemVersion: ruleItem.Version, RuleId: rule.RuleId, RuleItemId: ruleItem.Id})
			}
		}
		j.l.Info().Msgf("asssembler stats: %v", stats)
		assemblerRunOutput := &pb.AssemblerRunOutput{
			BidUnitConfigId:   bidUnitConfig.Id,
			ErrorBidsCount:    stats.errorBids,
			LogsS3Path:        stats.logsS3Path,
			AssembledBidsPath: stats.assembledBidsS3Path,
			IsError:           stats.isError,
			ErrorMessage:      stats.errorMessage,
		}
		if stats.isError {
			assemblerRunOutput.ErrorStage = pb.AssemblerRunOutputErrorStage_ASSEMBLER_RUN_OUTPUT_ERROR_STAGE_ASSEMBLY
		}
		assemblerRunOutputs = append(assemblerRunOutputs, assemblerRunOutput)
	}
	assemblerRun.Outputs = assemblerRunOutputs
	assemblerRun.RuleItems = assemblerRuleItems

	err = j.AssemblerClient.UpdateAssemblerRun(assemblerRun)
	if err != nil {
		return fmt.Errorf("update assembler run: %w", err)
	}
	return nil
}

func (j *Job) getAndLoadEnrichers(db *sql.DB, pipelineAssemblerConfig *pb.PipelineAssemblerConfig, runParameters map[string]interface{}) ([]enrichers.Interface, error) {
	enrichers, err := enrichers.GetImplementedEnrichers(pipelineAssemblerConfig, j.s3Utils, j.l, runParameters)
	if err != nil {
		return nil, fmt.Errorf("getEnrichers: %w", err)
	}
	for _, enricher := range enrichers {
		err = enricher.LoadData(db)
		if err != nil {
			return nil, fmt.Errorf("loadEnrichData: %w", err)
		}
	}
	return enrichers, nil
}

func (j *Job) getAndLoadFilterData(db *sql.DB, pipelineAssemblerConfig *pb.PipelineAssemblerConfig, runParameters map[string]interface{}) (filters.Interface, error) {
	filter, err := filters.GetFilter(pipelineAssemblerConfig, j.s3Utils, j.l, runParameters)
	if err != nil {
		return nil, fmt.Errorf("getFilter: %w", err)
	}
	if filter == nil {
		j.l.Info().Msgf("no filter configured, Skipping....")
		return nil, nil
	}

	err = filter.LoadData(db)
	if err != nil {
		return nil, fmt.Errorf("filterLoadData: %w", err)
	}
	return filter, nil
}

// preparebidUnitConfigs fetches bidUnitConfigs for all model runs and associated experiment configs and rules
func (j *Job) preparebidUnitConfigs(modelRuns []*pb.ModelRun, marketingChannel pbenums.MarketingChannel) ([]common.BidUnitConfig, error) {
	var bidUnitConfigs []common.BidUnitConfig
	for _, modelRun := range modelRuns {
		for _, modelRunOutput := range modelRun.Outputs {
			pbModel, err := j.modelClient.GetModel(modelRun.ModelId, marketingChannel)
			if err != nil {
				return nil, fmt.Errorf("modelClient.GetModel: %w", err)
			}
			pbBidUnit, err := j.bidUnitClient.GetBidUnit(modelRunOutput.BidUnitId, marketingChannel)
			if pbBidUnit.Status == pb.BidUnitStatus_BID_UNIT_STATUS_DISABLED {
				j.l.Info().Msgf("bid unit: %s is disabled, Skipping....", pbBidUnit.Id)
				continue
			}
			if err != nil {
				return nil, fmt.Errorf("bidUnitClient.GetBidUnit: %w", err)
			}
			pbBidUnitConfigs, err := j.bidUnitClient.GetBidUnitConfigs(modelRunOutput.BidUnitId, modelRun.ModelId)

			if err != nil {
				return nil, fmt.Errorf("getBidUnitConifg : %w", err)
			}

			for _, pbBidUnitConfig := range pbBidUnitConfigs {
				if pbBidUnitConfig.Status == pb.BidUnitConfigStatus_BID_UNIT_CONFIG_STATUS_DISABLED {
					j.l.Info().Msgf("bidUnitConfig: %s is disabled", pbBidUnitConfig.Id)
					j.l.Info().Msgf("ignoring estimates for bitUnitId: %s and modelId: %s", modelRunOutput.BidUnitId, pbBidUnitConfig.ModelId)
					continue
				}
				// fetch rules for each bidUnitConfig
				pbRules, err := j.ruleClient.GetRules(pbBidUnitConfig.AssemblerRuleIds, marketingChannel)
				if err != nil {
					return nil, fmt.Errorf("getRules: %w", err)
				}
				pbRuleItems, err := j.ruleClient.GetRuleItems(pbBidUnitConfig.AssemblerRuleIds)
				if err != nil {
					return nil, fmt.Errorf("getRuleItems: %w", err)
				}
				schemaIds := make([]string, len(pbRules))
				index := 0
				for _, pbRule := range pbRules {
					schemaIds[index] = pbRule.SchemaId
					index += 1
				}
				pbRuleSchemas, err := j.ruleClient.GetRuleSchemas(schemaIds)
				if err != nil {
					return nil, fmt.Errorf("getRuleSchemas: %w", err)
				}
				var rules []common.AssemblerRule
				for _, ruleId := range pbBidUnitConfig.AssemblerRuleIds {
					var ruleItems []common.RuleItem
					pbRuleItemsForRule, ok := pbRuleItems[ruleId]
					if !ok {
						return nil, fmt.Errorf("ruleItems not found for rule %s", ruleId)
					}
					for _, pbRuleItem := range pbRuleItemsForRule {
						if pbRuleItem.Status == pb.RuleItemStatus_RULE_ITEM_STATUS_DISABLED {
							j.l.Info().Msgf("rule item: %s is disabled, skipping...", pbRuleItem.Id)
							continue
						}
						ruleItems = append(ruleItems, common.RuleItem{Id: pbRuleItem.Id, Info: pbRuleItem.Info, Version: pbRuleItem.Version})
					}
					pbRule, ok := pbRules[ruleId]
					if !ok {
						return nil, fmt.Errorf("pbRule not found for ruleId %s", ruleId)
					}
					pbRuleSchema, ok := pbRuleSchemas[pbRule.SchemaId]
					if !ok {
						return nil, fmt.Errorf("pbRuleSchema not found for ruleId %s", ruleId)
					}

					rules = append(rules, common.AssemblerRule{
						RuleId:     ruleId,
						RuleSchema: pbRuleSchema.Name,
						RuleName:   pbRule.Name,
						RuleItems:  ruleItems,
					})
				}
				experimentConfig := common.ExperimentConfig{
					ExperimentId:   pbBidUnitConfig.ExperimentConfig.ExperimentId,
					ExperimentName: pbBidUnitConfig.ExperimentConfig.ExperimentName,
					BinId:          pbBidUnitConfig.ExperimentConfig.BinId,
				}
				bidUnitConfig := common.BidUnitConfig{
					Rules:            rules,
					Id:               pbBidUnitConfig.Id,
					ExperimentConfig: experimentConfig,
					Type:             common.GetBidUnitConfigType(pbBidUnitConfig.Type),
					EstimatesPath:    modelRunOutput.BidsS3Path,
					ModelId:          modelRun.ModelId,
					ModelName:        pbModel.Name,
					ModelVersion:     pbModel.Version,
					AssemberRunId:    j.runId,
					BidUnitId:        pbBidUnit.Id,
					BidUnitName:      pbBidUnit.Name,
				}
				bidUnitConfigs = append(bidUnitConfigs, bidUnitConfig)
			}
		}
	}
	return bidUnitConfigs, nil
}

// extractModelRunIds extracts the ModelRunIds from a list of ModelRuns.
func extractModelRunIds(modelRuns []*pb.PipelineModelRun) []string {
	var modelRunIds []string
	for _, modelRun := range modelRuns {
		modelRunIds = append(modelRunIds, modelRun.ModelRunId)
	}
	return modelRunIds
}
