package config

import (
	"fmt"

	"github.com/Netflix/go-env"
)

type EntityConfig struct {
	LokiSvcAddr string `env:"LOKI_SERVICE_ADDR,default=localhost:50051"`
	TrackingUrl string `env:"TRACKING_URL,default=https://argo-workflows.test.marketing.expedia.com/workflows/loki"`
	AwsRegion   string `env:"AWS_REGION,default=us-east-1"`
	LokiS3Path  string `env:"S3_PATH,default=s3://eg-marketing-platform-prod/loki"`
	Env         string `env:"ENV,default=DEV"`
	WorkDir     string `env:"WORK_DIR,default=./workspace"`
}

func GetEntityConfig() (*EntityConfig, error) {
	var config EntityConfig

	_, err := env.UnmarshalFromEnviron(&config)
	if err != nil {
		return nil, fmt.Errorf("failed to unmarshal assembler config: %w", err)
	}
	return &config, nil
}
