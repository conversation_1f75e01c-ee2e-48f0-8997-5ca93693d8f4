package config

import (
	"fmt"

	"github.com/Netflix/go-env"
)

type BidAssemblyConfig struct {
	LokiSvcAddr      string `env:"LOKI_SERVICE_ADDR,default=localhost:50051"`
	TrackingUrl      string `env:"TRACKING_URL,default=https://argo-workflows.test.marketing.expedia.com/workflows/loki"`
	MarketLabSvcAddr string `env:"MARKETLAB_SERVICE_ADDR,default=https://meta-experiment-service.rcp.us-east-1.marketing.test.exp-aws.net/v2/experiments?status=active"` // Valid status values: active, completed. Remove `?status=active` to get all experiments.
	AwsRegion        string `env:"AWS_REGION,default=us-west-2"`
	LokiS3Path       string `env:"S3_PATH,default=s3://eg-marketing-platform-test/loki"`
	Env              string `env:"ENV,default=DEV"`
	WorkDir          string `env:"WORK_DIR,default=./workspace"`
}

func GetBidAssemblyConfig() (*BidAssemblyConfig, error) {
	var config BidAssemblyConfig

	_, err := env.UnmarshalFromEnviron(&config)
	if err != nil {
		return nil, fmt.Errorf("failed to unmarshal bidassembly config: %w", err)
	}
	return &config, nil
}
