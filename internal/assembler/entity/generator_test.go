package entity

import (
	"context"
	"fmt"
	"os"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/suite"
	"github.expedia.biz/gmo-performance-marketing/loki/internal/assembler/common"
	"github.expedia.biz/gmo-performance-marketing/sem-lib-go/pkg/logger"
	"github.expedia.biz/gmo-performance-marketing/sem-lib-go/pkg/mock"
)

type GeneratorTest struct {
	suite.Suite

	generator *generator
}

var ctx = context.Background()

func (g *GeneratorTest) SetupSuite() {
	l := logger.Get()

	awsClient := &mock.AwsClient{}
	generatorCfg := &generatorConfig{
		workDir:      "../../../test/data/bidUnitConfigId=test",
		entitiesFile: "../../../test/data/entities.bin",
	}
	g.generator, _ = NewGenerator(generatorCfg, l, awsClient)
	g.generator.init()
}

func (g *GeneratorTest) TearDownSuite() {
	err := deleteFileOrDir("../../../test/data/entities.bin")
	if err != nil {
		g.generator.logger.Error().Msgf("error deleting entities file: %v", err)
	}
}

func deleteFileOrDir(path string) error {
	// Check if the path exists
	info, err := os.Stat(path)
	if err != nil {
		return fmt.Errorf("could not access path %s: %v", path, err)
	}

	// If it's a directory, call the recursive function to delete it
	if info.IsDir() {
		return os.RemoveAll(path) // RemoveAll will delete the directory and its contents
	}

	// If it's a file, delete it directly
	return os.Remove(path) // Remove the file
}

func TestGeneratorRunSuite(t *testing.T) {
	suite.Run(t, new(GeneratorTest))
}

func (g *GeneratorTest) TestGenerator() {
	g.TestRun()
}

func (g *GeneratorTest) TestRun() {
	bidUnitConfig := &common.BidUnitConfig{Id: "test", EstimatesPath: "s3://test"}
	err := g.generator.run(bidUnitConfig)
	requireNoError(g.T(), err)
	assert.Equal(g.T(), int64(2), bidUnitConfig.SuccessBids)
	assert.Equal(g.T(), int64(1), bidUnitConfig.ErrorBids)
}

func requireNoError(t testing.TB, err error) {
	t.Helper()
	if err != nil {
		t.Fatal(err)
	}
}
