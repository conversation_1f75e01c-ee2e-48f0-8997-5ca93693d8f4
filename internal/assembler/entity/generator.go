package entity

import (
	"encoding/binary"
	"encoding/csv"
	"fmt"
	"io"
	"io/fs"
	"os"
	"path/filepath"
	"strings"

	"github.com/rs/zerolog"
	"github.expedia.biz/gmo-performance-marketing/loki/internal/assembler/common"
	entityv1 "github.expedia.biz/gmo-performance-marketing/pb-go/pkg/entity/v1"
	"github.expedia.biz/gmo-performance-marketing/sem-lib-go/pkg/aws"
	"github.expedia.biz/gmo-performance-marketing/sem-lib-go/pkg/protoserde"
)

const sizeBufferLen = binary.MaxVarintLen64

type generatorConfig struct {
	workDir      string
	entitiesFile string
}

type generator struct {
	awsClient    aws.Client
	generatorCfg *generatorConfig
	logger       zerolog.Logger
	bidsPath     string
}

// NewGenerator creates and returns a new generator instance.
func NewGenerator(cfg *generatorConfig, l zerolog.Logger, awsClient aws.Client) (*generator, error) {
	return &generator{
		awsClient:    awsClient,
		generatorCfg: cfg,
		logger:       l,
	}, nil
}

// init initializes directories defined in the config.
func (g *generator) init() {
	g.bidsPath = filepath.Join(g.generatorCfg.workDir, "bids")
	// Initialize necessary directories
	g.createDirIfNotExist(g.bidsPath)
}

// createDirIfNotExist creates a directory if it doesn't exist already.
func (g *generator) createDirIfNotExist(path string) {
	_ = os.MkdirAll(path, os.ModePerm)
}

// run orchestrates the entire generation process, downloading files and processing them.
func (g *generator) run(bidUnitConfig *common.BidUnitConfig) error {

	bidsPath := bidUnitConfig.FinalBidsPath
	if bidUnitConfig.EstimatesPath != "" {
		bidsPath = bidUnitConfig.EstimatesPath
	}
	if bidsPath == "" {
		return fmt.Errorf("bids path is empty, either estimates path or final bids path should be specified")
	}

	g.logger.Info().Msgf("Listing S3 files under path: %s", bidsPath)

	// List S3 files
	s3Objects, err := g.awsClient.S3List(aws.S3Uri(bidsPath))
	if err != nil {
		return fmt.Errorf("list files: %w", err)
	}

	// Download S3 files
	for _, s3Object := range s3Objects {
		s3KeySplit := strings.Split(*s3Object.Key, "/")
		fileName := s3KeySplit[len(s3KeySplit)-1]
		objectLocalPath := fmt.Sprintf("%s/%s", g.bidsPath, fileName)
		s3Uri := aws.S3Uri(fmt.Sprintf("s3://%s/%s", *s3Object.Bucket, *s3Object.Key))
		g.logger.Info().Msgf("Downloading file: %s", *s3Object.Key)
		_, err := g.awsClient.S3DownloadFile(s3Uri, objectLocalPath)
		if err != nil {
			return fmt.Errorf("downloadFile: %w", err)
		}
	}

	// Process downloaded files
	g.logger.Info().Msgf("Listing bid files under path: %s", g.bidsPath)
	files, err := getBidFiles(g.bidsPath)
	if err != nil {
		return fmt.Errorf("getBidFiles: %w", err)
	}

	for _, file := range files {
		g.logger.Info().Msgf("Generating entities for file: %s", file)
		err := g.generateEntities(file, bidUnitConfig)
		if err != nil {
			return fmt.Errorf("generateEntities: %w", err)
		}
	}

	// Log stats
	g.logger.Info().Msgf("SuccessCounts: %d, ErrorCounts: %d", bidUnitConfig.SuccessBids, bidUnitConfig.ErrorBids)
	g.logger.Info().Msgf("Successfully generated entities for bidUnitConfig: %s", bidUnitConfig.Id)

	return nil
}

// generateEntities processes the given bid file and generates the corresponding entities.
func (g *generator) generateEntities(bidsPath string, bidUnitConfig *common.BidUnitConfig) error {
	// Open entity file and handle errors
	entitiesFile, err := g.openFile(g.generatorCfg.entitiesFile, os.O_APPEND|os.O_CREATE|os.O_WRONLY)
	if err != nil {
		return err
	}
	defer entitiesFile.Close()

	// Open bids file for reading
	reader, err := os.Open(bidsPath)
	if err != nil {
		return fmt.Errorf("openFile: %w", err)
	}
	defer reader.Close()

	csvReader := csv.NewReader(reader)
	headers, err := csvReader.Read()
	if err != nil {
		return fmt.Errorf("csvReader.Read: %w", err)
	}

	// Prepare the converter
	protoConvertor, err := protoserde.NewFlatConverter[*entityv1.MarketingEntity](headers)
	if err != nil {
		return fmt.Errorf("protoConvertor: %w", err)
	}

	// Read and process each bid row
	for {
		bid, err := csvReader.Read()
		if err == io.EOF {
			break
		} else if err != nil {
			g.handleBidError(bid, err, bidUnitConfig)
			continue
		}

		// Convert bid to proto
		bidProto, err := protoConvertor.Convert(bid)
		if err != nil {
			g.handleBidError(bid, err, bidUnitConfig)
			continue
		}

		// Write the proto to the entities file
		if err := g.writeProtoToFile(entitiesFile, bidProto); err != nil {
			g.handleBidError(bid, err, bidUnitConfig)
			continue
		}

		bidUnitConfig.SuccessBids++
	}

	return nil
}

// openFile is a utility to open files and handle errors.
func (g *generator) openFile(path string, flag int) (*os.File, error) {
	file, err := os.OpenFile(path, flag, os.ModePerm)
	if err != nil {
		g.logger.Warn().Msgf("openFile: %v", err)
		return nil, fmt.Errorf("openFile: %w", err)
	}
	return file, nil
}

// handleBidError handles errors while processing bids, writing them to the partial failure file.
func (g *generator) handleBidError(bid []string, err error, bidStats *common.BidUnitConfig) {
	g.logger.Warn().Msgf("Bid error: %v", err)
	bidStats.ErrorBids++
}

// writeProtoToFile writes a proto message to the specified file.
func (g *generator) writeProtoToFile(file *os.File, bidProto *entityv1.MarketingEntity) error {
	sizePrefixBuf := make([]byte, sizeBufferLen)
	return protoserde.WriteDelimitedProtoMessage(file, bidProto, sizePrefixBuf)
}

// getBidFiles returns a list of bid files from the specified directory.
func getBidFiles(bidsPath string) ([]string, error) {
	var bidFiles []string
	err := filepath.WalkDir(bidsPath, func(path string, d fs.DirEntry, err error) error {
		if err != nil {
			return err
		}
		if !d.IsDir() && filepath.Ext(path) == ".csv" {
			bidFiles = append(bidFiles, path)
		}
		return nil
	})

	if err != nil {
		return nil, fmt.Errorf("filePathWalk: %v", err)
	}
	return bidFiles, nil
}
