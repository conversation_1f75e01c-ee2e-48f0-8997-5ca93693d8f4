package entity

import (
	"testing"

	"github.com/rs/zerolog"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/suite"
	"github.expedia.biz/gmo-performance-marketing/loki/internal/assembler/config"
	"github.expedia.biz/gmo-performance-marketing/loki/internal/client"
	mock3 "github.expedia.biz/gmo-performance-marketing/loki/internal/mock"
	"github.expedia.biz/gmo-performance-marketing/sem-lib-go/pkg/aws"
	"github.expedia.biz/gmo-performance-marketing/sem-lib-go/pkg/logger"
	"github.expedia.biz/gmo-performance-marketing/sem-lib-go/pkg/mock"
)

type JobTest struct {
	suite.Suite
	assemblerClient client.AssemblerClient
	modelClient     client.ModelClient
	pipelineClient  client.PipeLineClient
	awsClient       aws.Client
	bidUnitClient   client.BidUnitClient
	job             *Job
	l               zerolog.Logger
}

func (j *JobTest) SetupSuite() {
	// assembler mock client
	assemblerClient, err := mock3.NewAssemblerClient()
	requireNoError(j.T(), err)
	j.assemblerClient = assemblerClient

	// pipeline mock client
	j.pipelineClient, err = mock3.NewPipelineClient()
	requireNoError(j.T(), err)

	// model client
	j.modelClient, err = mock3.NewModelClient()
	requireNoError(j.T(), err)
	// aws client
	j.awsClient = &mock.AwsClient{}
	j.l = logger.Get()
	// bid unit client
	j.bidUnitClient, err = mock3.NewBidUnitClient()
	requireNoError(j.T(), err)

}

func (j *JobTest) TearDownSuite() {
	err := deleteFileOrDir("../../../test/data/entities.bin")
	if err != nil {
		j.l.Error().Msgf("error deleting entities file: %v", err)
	}
}

func TestJobRunSuite(t *testing.T) {
	suite.Run(t, new(JobTest))
}

func (j *JobTest) TestSuccessAssemblerRun() {
	l := logger.Get()

	cfg := &config.EntityConfig{Env: "DEV", WorkDir: "../../../test/data"}
	runId := "test-assembler-run-1"
	workflowId := "test-assembler-workflow-1"

	job := &Job{l: l, cfg: cfg, runId: runId, workflowId: workflowId}
	job.AssemblerClient = j.assemblerClient
	job.modelClient = j.modelClient
	job.pipelineClient = j.pipelineClient
	job.awsClient = j.awsClient
	job.bidUnitClient = j.bidUnitClient

	run, err := job.AssemblerClient.GetAssemblerRun(job.runId)
	requireNoError(j.T(), err)

	err = job.Run(run)
	requireNoError(j.T(), err)
	output := run.Outputs[0]

	assert.Equal(j.T(), int64(2), output.SuccessBidsCount)
	assert.Equal(j.T(), int64(1), output.ErrorBidsCount)
}

func (j *JobTest) TestFailureAssemblerRun() {
	l := logger.Get()

	cfg := &config.EntityConfig{Env: "DEV"}
	runId := "test-assembler-run-2"
	workflowId := "test-assembler-workflow-1"

	job := &Job{l: l, cfg: cfg, runId: runId, workflowId: workflowId}
	job.AssemblerClient = j.assemblerClient
	job.modelClient = j.modelClient
	job.pipelineClient = j.pipelineClient
	job.awsClient = j.awsClient

	run, err := job.AssemblerClient.GetAssemblerRun(job.runId)
	requireNoError(j.T(), err)

	err = job.Run(run)
	assert.Error(j.T(), err)
}
