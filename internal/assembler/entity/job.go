package entity

import (
	"encoding/json"
	"fmt"
	"strings"

	"github.com/rs/zerolog"
	"github.expedia.biz/gmo-performance-marketing/loki/internal/assembler/common"
	"github.expedia.biz/gmo-performance-marketing/loki/internal/assembler/config"
	"github.expedia.biz/gmo-performance-marketing/loki/internal/client"
	pb "github.expedia.biz/gmo-performance-marketing/pb-go/pkg/loki/v1"
	"github.expedia.biz/gmo-performance-marketing/sem-lib-go/pkg/aws"
)

type Job struct {
	awsClient       aws.Client
	modelClient     client.ModelClient
	pipelineClient  client.PipeLineClient
	bidUnitClient   client.BidUnitClient
	AssemblerClient client.AssemblerClient

	l          zerolog.Logger
	cfg        *config.EntityConfig
	runId      string
	workflowId string
}

// NewJob initializes a new Job instance.
func NewJob(l zerolog.Logger, cfg *config.EntityConfig, runId string, workflowId string) *Job {
	return &Job{l: l, cfg: cfg, runId: runId, workflowId: workflowId}
}

// Init initializes the various clients for the Job.
func (j *Job) Init() error {
	// Create assembler client
	var err error
	j.AssemblerClient, err = client.NewAssemblerClient(j.cfg.LokiSvcAddr, j.l)
	if err != nil {
		return fmt.Errorf("init assembler client: %w", err)
	}

	// get model svc client
	j.modelClient, err = client.GetNewModelClient(j.cfg.LokiSvcAddr, j.l)
	if err != nil {
		return fmt.Errorf("init model client: %w", err)
	}

	// get pipeline service client
	j.pipelineClient, err = client.GetNewPipelineClient(j.l, j.cfg.LokiSvcAddr)
	if err != nil {
		return fmt.Errorf("init pipeline service client: %w", err)
	}

	// get bidUnit Client
	j.bidUnitClient, err = client.NewBidUnitClient(j.l, j.cfg.LokiSvcAddr)
	if err != nil {
		return fmt.Errorf("init bid unit client: %w", err)
	}

	j.awsClient, err = aws.NewClient(j.cfg.AwsRegion)
	if err != nil {
		return fmt.Errorf("new aws client: %w", err)
	}

	return nil
}

func (j *Job) Run(run *pb.AssemblerRun) error {

	// Fetch pipeline run details
	pipelineRun, err := j.pipelineClient.GetPipelineRun(run.PipelineRunId)
	if err != nil {
		return fmt.Errorf("get pipeline run: %w", err)
	}

	var runParametersMap map[string]interface{}
	err = json.Unmarshal([]byte(run.RunParameters), &runParametersMap)
	if err != nil {
		return fmt.Errorf("unmarshal run parameters: %w", err)
	}

	var bidUnitConfigs []*common.BidUnitConfig

	// if outputs are empty, it means there was no assembly step
	if len(run.Outputs) == 0 {
		bidUnitConfigs, err = j.prepareBidUnitConfigs(pipelineRun)
	} else {
		bidUnitConfigs, err = j.prepareBidUnitConfigsFromAssemblerRunOutputs(run.Outputs)
	}
	if err != nil {
		return fmt.Errorf("prepare bid unit configs: %w", err)
	}

	// Process each bid unit
	err = j.processBidUnitConfigs(bidUnitConfigs, run)
	if err != nil {
		return err
	}

	// Update assembler run outputs
	if err := j.AssemblerClient.UpdateAssemblerRun(run); err != nil {
		return fmt.Errorf("update assembler run outputs: %w", err)
	}
	return nil
}

func (j *Job) prepareBidUnitConfigs(pipelineRun *pb.PipelineRun) ([]*common.BidUnitConfig, error) {
	bidUnitConfigs := make([]*common.BidUnitConfig, 0)

	var modelRunIds []string
	for _, modelRun := range pipelineRun.ModelRuns {
		modelRunIds = append(modelRunIds, modelRun.ModelRunId)
	}

	modelRuns, err := j.modelClient.GetModelRuns(modelRunIds)
	if err != nil {
		return nil, fmt.Errorf("get model runs: %w", err)
	}

	for _, modelRun := range modelRuns {
		for _, modelRunOutput := range modelRun.Outputs {

			pbBidUnitConfigs, err := j.bidUnitClient.GetBidUnitConfigs(modelRunOutput.BidUnitId, modelRun.ModelId)
			if err != nil {
				return nil, fmt.Errorf("get bid unit configs: %w", err)
			}

			for _, pbBidUnitConfig := range pbBidUnitConfigs {
				bidUnitConfigs = append(bidUnitConfigs, &common.BidUnitConfig{
					Id:            pbBidUnitConfig.Id,
					Type:          common.GetBidUnitConfigType(pbBidUnitConfig.Type),
					EstimatesPath: modelRunOutput.BidsS3Path,
					ModelId:       modelRun.ModelId,
					AssemberRunId: j.runId,
					BidUnitId:     pbBidUnitConfig.BidUnitId,
				})
			}
		}
	}

	return bidUnitConfigs, nil
}

func (j *Job) prepareBidUnitConfigsFromAssemblerRunOutputs(outputs []*pb.AssemblerRunOutput) ([]*common.BidUnitConfig, error) {
	var bidUnitConfigs []*common.BidUnitConfig

	for _, output := range outputs {
		pbBidUnitConfig, err := j.bidUnitClient.GetBidUnitConfigsById(output.BidUnitConfigId)
		if err != nil {
			return nil, err
		}
		bidUnitConfigs = append(bidUnitConfigs, &common.BidUnitConfig{
			Id:            output.BidUnitConfigId,
			Type:          common.GetBidUnitConfigType(pbBidUnitConfig.Type),
			AssemberRunId: j.runId,
			ModelId:       pbBidUnitConfig.ModelId,
			BidUnitId:     pbBidUnitConfig.BidUnitId,

			IsError:           output.IsError,
			Error:             output.ErrorMessage,
			SuccessBids:       output.SuccessBidsCount,
			ErrorBids:         output.ErrorBidsCount,
			LogsPath:          output.LogsS3Path,
			AssembledBidsPath: output.AssembledBidsPath,
			FinalBidsPath:     output.FinalBidsPath,
		})
	}
	return bidUnitConfigs, nil
}

func (j *Job) processBidUnitConfigs(bidUnitConfigs []*common.BidUnitConfig, run *pb.AssemblerRun) error {

	baseS3Path := fmt.Sprintf(common.AssemblerS3Path, j.cfg.LokiS3Path, j.runId)
	entitiesPath := fmt.Sprintf("%s/entities.bin", baseS3Path)
	localEntitiesPath := fmt.Sprintf("%s/entities.bin", j.cfg.WorkDir)
	for _, bidUnitConfig := range bidUnitConfigs {
		j.l.Info().Msgf("processing bid unit config %s", bidUnitConfig.Id)
		if bidUnitConfig.IsError {
			j.l.Info().Msgf("There was an error marked for this config. Skipping...")
			continue
		}

		workDir := fmt.Sprintf("%s/bidUnitConfigId=%s", j.cfg.WorkDir, bidUnitConfig.Id)

		runGenerator := func() {
			generatorCfg := &generatorConfig{
				workDir:      workDir,
				entitiesFile: localEntitiesPath,
			}
			generator, err := NewGenerator(generatorCfg, j.l, j.awsClient)
			if err != nil {
				bidUnitConfig.IsError = true
				bidUnitConfig.Error = err.Error()
			}
			generator.init()

			err = generator.run(bidUnitConfig)
			if err != nil {
				bidUnitConfig.IsError = true
				bidUnitConfig.Error = err.Error()
			}
		}
		if bidUnitConfig.Type != common.OBSERVE {
			runGenerator()
		}

		bidUnitConfigS3Path := fmt.Sprintf("%s/bidUnitConfigId=%s/", baseS3Path, bidUnitConfig.Id)
		// Upload artifacts
		if err := j.uploadArtifacts(bidUnitConfigS3Path, workDir); err != nil {
			if err != nil {
				j.l.Error().Err(err).Msg("upload artifacts")
				continue
			}
		}
	}
	err := j.uploadEntities(entitiesPath, localEntitiesPath)
	if err != nil {
		return err
	}
	run.Outputs = toAssemblerRunOutputs(bidUnitConfigs)
	run.MarketingEntitiesS3Path = entitiesPath
	return nil
}

func toAssemblerRunOutputs(bidUnitConfigs []*common.BidUnitConfig) []*pb.AssemblerRunOutput {
	runOutputs := make([]*pb.AssemblerRunOutput, 0)
	for _, bidUnitConfig := range bidUnitConfigs {
		runOutput := &pb.AssemblerRunOutput{
			BidUnitConfigId:   bidUnitConfig.Id,
			LogsS3Path:        bidUnitConfig.LogsPath,
			AssembledBidsPath: bidUnitConfig.AssembledBidsPath,
			FinalBidsPath:     bidUnitConfig.FinalBidsPath,
			SuccessBidsCount:  bidUnitConfig.SuccessBids,
			ErrorBidsCount:    bidUnitConfig.ErrorBids,
			IsError:           bidUnitConfig.IsError,
			ErrorMessage:      bidUnitConfig.Error,
		}
		if runOutput.IsError {
			runOutput.ErrorStage = pb.AssemblerRunOutputErrorStage_ASSEMBLER_RUN_OUTPUT_ERROR_STAGE_ENTITY
		}
		runOutputs = append(runOutputs, runOutput)
	}
	return runOutputs
}

// uploadArtifacts uploads the generated artifacts to S3.
func (j *Job) uploadArtifacts(s3Path string, localPath string) error {
	if strings.ToLower(j.cfg.Env) == "dev" {
		return nil
	}
	_, err := j.awsClient.S3UploadDir(localPath, aws.S3Uri(s3Path))
	if err != nil {
		j.l.Error().Err(err).Msgf("Failed to upload %s to S3", s3Path)
		return err
	}
	return nil
}

func (j *Job) uploadEntities(s3Path string, localPath string) error {
	if strings.ToLower(j.cfg.Env) == "dev" {
		return nil
	}
	err := j.awsClient.S3UploadFile(localPath, aws.S3Uri(s3Path))
	if err != nil {
		j.l.Error().Err(err).Msgf("Failed to upload %s to S3", s3Path)
		return err
	}
	return nil
}
