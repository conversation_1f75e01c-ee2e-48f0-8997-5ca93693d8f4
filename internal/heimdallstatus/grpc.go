package heimdallstatus

import (
	"context"

	heimdallpb "github.expedia.biz/gmo-performance-marketing/pb-go/pkg/argo/heimdall/v1"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
)

func (s *Service) ReceiveStatus(
	ctx context.Context, req *heimdallpb.ReceiveStatusRequest,
) (*heimdallpb.ReceiveStatusResponse, error) {
	updateHandler, found := s.clientHandlers[req.StartedBy]
	if !found {
		return nil, status.Errorf(codes.Internal, "clientNotRegistered: %s", req.StartedBy)
	}

	err := updateHandler(UpdateHandlerRequest{
		WorkflowId:    req.WorkflowId,
		OldStatus:     req.OldStatus,
		NewStatus:     req.NewStatus,
		StatusMessage: req.StatusMessage,
	})
	if err != nil {
		return nil, status.Errorf(codes.Internal, "updateHandler: %v", err)
	}

	return &heimdallpb.ReceiveStatusResponse{}, nil
}
