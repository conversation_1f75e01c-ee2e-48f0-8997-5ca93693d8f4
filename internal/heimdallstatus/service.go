package heimdallstatus

import (
	"fmt"

	"github.com/rs/zerolog"
	pbheimdall "github.expedia.biz/gmo-performance-marketing/pb-go/pkg/argo/heimdall/v1"
	"google.golang.org/grpc"
)

type Service struct {
	pbheimdall.UnimplementedStatusServiceServer

	clientHandlers map[string]UpdateHandler
	logger         zerolog.Logger
}

func NewService(logger zerolog.Logger) *Service {
	return &Service{
		clientHandlers: map[string]UpdateHandler{},
		logger:         logger,
	}
}

func RegisterService(s grpc.ServiceRegistrar, svc *Service) {
	pbheimdall.RegisterStatusServiceServer(s, svc)
}

func (s *Service) RegisterClient(client string, updateHandler UpdateHandler) error {
	_, found := s.clientHandlers[client]
	if found {
		return fmt.Errorf("duplicateClient: %s", client)
	}

	s.clientHandlers[client] = updateHandler
	return nil
}
