package pipeline

import (
	"fmt"
	"strconv"
	"time"

	"github.com/lib/pq"
	pbenums "github.expedia.biz/gmo-performance-marketing/pb-go/pkg/enums/v1"
	pb "github.expedia.biz/gmo-performance-marketing/pb-go/pkg/loki/v1"
	"github.expedia.biz/gmo-performance-marketing/sem-lib-go/pkg/protoserde"
	"google.golang.org/protobuf/proto"
)

type Pipeline struct {
	Id uint64 `gorm:"primaryKey"`

	Name             string `gorm:"uniqueIndex:uidx_pipeline_name"`
	MarketingChannel int32
	Status           int32
	CronExpression   string
	Labels           map[string]string `gorm:"type:json;serializer:json"`
	ContactDls       pq.StringArray    `gorm:"type:text[]"`

	ModelConfigs       []byte
	AssemblerConfig    []byte
	PublishConfig      []byte
	PostPublishConfigs []byte

	CreatedBy         string
	UpdatedBy         string
	CreatedAt         time.Time `gorm:"autoCreateTime"`
	UpdatedAt         time.Time `gorm:"autoUpdateTime"`
	LastCheckedAt     time.Time
	LastPipelineRunId uint64
}

func (Pipeline) TableName() string {
	return "loki.pipelines"
}

func (p *Pipeline) toPb() (*pb.Pipeline, error) {
	modelConfigs := make([]*pb.PipelineModelConfig, 0)
	err := protoserde.UnmarshalArray(p.ModelConfigs, &modelConfigs)
	if err != nil {
		return nil, fmt.Errorf("unmarshalModelConfigs: %w", err)
	}
	assemblerConfig := &pb.PipelineAssemblerConfig{}
	err = proto.Unmarshal(p.AssemblerConfig, assemblerConfig)
	if err != nil {
		return nil, fmt.Errorf("unmarshalAssemblerConfig: %w", err)
	}
	publishConfig := &pb.PipelinePublishConfig{}
	err = proto.Unmarshal(p.PublishConfig, publishConfig)
	if err != nil {
		return nil, fmt.Errorf("unmarshalPublishConfig: %w", err)
	}
	postPublishConfigs := make([]*pb.PipelinePostPublishConfig, 0)
	err = protoserde.UnmarshalArray(p.PostPublishConfigs, &postPublishConfigs)
	if err != nil {
		return nil, fmt.Errorf("unmarshalPostPublishConfigs: %w", err)
	}

	return &pb.Pipeline{
		Id:                 strconv.FormatUint(p.Id, 10),
		Name:               p.Name,
		MarketingChannel:   pbenums.MarketingChannel(p.MarketingChannel),
		ModelConfigs:       modelConfigs,
		AssemblerConfig:    assemblerConfig,
		PublishConfig:      publishConfig,
		PostPublishConfigs: postPublishConfigs,
		CronExpression:     p.CronExpression,
		Labels:             p.Labels,
		ContactDls:         p.ContactDls,
		CreatedBy:          p.CreatedBy,
		CreatedAt:          protoserde.ToPbTimestamp(p.CreatedAt),
		Status:             pb.PipelineStatus(p.Status),
		LastCheckedAt:      protoserde.ToPbTimestamp(p.LastCheckedAt),
		LastPipelineRunId:  strconv.FormatUint(p.LastPipelineRunId, 10),
	}, nil
}

type PipelineRun struct {
	Id         uint64 `gorm:"primaryKey"`
	PipelineId uint64

	MarketingChannel int32

	Status          int32
	ModelRuns       []byte
	AssemblerRun    []byte
	PublishRun      []byte
	PostPublishRuns []byte

	CreatedBy    string
	ApprovedBy   string
	CreatedAt    time.Time `gorm:"autoCreateTime"`
	UpdatedAt    time.Time `gorm:"autoUpdateTime"`
	FinishedAt   time.Time
	IsError      bool
	ErrorMessage string

	PipelineRef Pipeline `gorm:"foreignKey:PipelineId;references:Id;constraint:OnDelete:CASCADE"`
}

func (PipelineRun) TableName() string {
	return "loki.pipeline_runs"
}

func (p *PipelineRun) toPb() (*pb.PipelineRun, error) {
	modelRuns := make([]*pb.PipelineModelRun, 0)
	err := protoserde.UnmarshalArray(p.ModelRuns, &modelRuns)
	if err != nil {
		return nil, fmt.Errorf("unmarshalModelRuns: %w", err)
	}
	assemblerRun := &pb.PipelineAssemblerRun{}
	err = proto.Unmarshal(p.AssemblerRun, assemblerRun)
	if err != nil {
		return nil, fmt.Errorf("unmarshalAssemblerRun: %w", err)
	}
	publishRun := &pb.PipelinePublishRun{}
	err = proto.Unmarshal(p.PublishRun, publishRun)
	if err != nil {
		return nil, fmt.Errorf("unmarshalPublishRun: %w", err)
	}
	postPublishRuns := make([]*pb.PipelinePostPublishRun, 0)
	err = protoserde.UnmarshalArray(p.PostPublishRuns, &postPublishRuns)
	if err != nil {
		return nil, fmt.Errorf("unmarshalPostPublishRuns: %w", err)
	}

	return &pb.PipelineRun{
		Id:               strconv.FormatUint(p.Id, 10),
		PipelineId:       strconv.FormatUint(p.PipelineId, 10),
		Status:           pb.PipelineRunStatus(p.Status),
		ModelRuns:        modelRuns,
		AssemblerRun:     assemblerRun,
		PublishRun:       publishRun,
		PostPublishRuns:  postPublishRuns,
		MarketingChannel: pbenums.MarketingChannel(p.MarketingChannel),
		CreatedBy:        p.CreatedBy,
		CreatedAt:        protoserde.ToPbTimestamp(p.CreatedAt),
		UpdatedAt:        protoserde.ToPbTimestamp(p.UpdatedAt),
		FinishedAt:       protoserde.ToPbTimestamp(p.FinishedAt),
		IsError:          p.IsError,
		ErrorMessage:     p.ErrorMessage,
		ApprovedBy:       p.ApprovedBy,
	}, nil
}
