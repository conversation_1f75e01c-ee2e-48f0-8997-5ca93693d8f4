package pipeline

import (
	"fmt"

	"github.com/santhosh-tekuri/jsonschema/v5"
	"github.expedia.biz/gmo-performance-marketing/loki/internal/client"
	"github.expedia.biz/gmo-performance-marketing/loki/internal/heimdallstatus"
	"github.expedia.biz/gmo-performance-marketing/loki/internal/notification"
	heimdallv1 "github.expedia.biz/gmo-performance-marketing/pb-go/pkg/argo/heimdall/v1"
	publisherv1 "github.expedia.biz/gmo-performance-marketing/pb-go/pkg/publisher/v1"

	env "github.com/Netflix/go-env"
	"github.com/rs/zerolog"
	pb "github.expedia.biz/gmo-performance-marketing/pb-go/pkg/loki/v1"
	libgrpc "github.expedia.biz/gmo-performance-marketing/sem-lib-go/pkg/grpc"
	"google.golang.org/grpc"
	"gorm.io/gorm"
)

func RegisterService(
	s grpc.ServiceRegistrar,
	orm *gorm.DB,
	logger zerolog.Logger,
	heimdallStatusSvc *heimdallstatus.Service,
	notificationClient *notification.Notifier,
) error {
	svc, err := newService(orm, logger, notificationClient)
	if err != nil {
		return err
	}

	pb.RegisterPipelineServiceServer(s, svc)
	logger.Info().Msg("registered pipeline service")

	heimdallStatusSvc.RegisterClient(svc.heimdallStartedBy, svc.HandleHeimdallStatusUpdate)
	return nil
}

type serviceConfig struct {
	LokiSvcAddr             string `env:"LOKI_SERVICE_ADDR,default=localhost:50051"`
	OdinSvcAddr             string `env:"ODIN_SERVICE_ADDR,default=localhost:50051"`
	HeimdallStatusSvcServer string `env:"HEIMDALL_STATUS_SVC_SERVER,default=sem-misc-go.test.marketing.expedia.com:443"`
}

type service struct {
	pb.UnimplementedPipelineServiceServer

	cfg               serviceConfig
	orm               *gorm.DB
	logger            zerolog.Logger
	pipelineClient    pb.PipelineServiceClient
	heimdallClient    heimdallv1.WorkflowServiceClient
	flyteClient       client.FlyteClient
	publisherClient   publisherv1.PublisherServiceClient
	assemblerClient   pb.AssemblerServiceClient
	modelClient       pb.ModelServiceClient
	postPublishClient pb.PostPublishServiceClient
	schemaCompiler    *jsonschema.Compiler

	heimdallStartedBy  string
	notificationClient *notification.Notifier
}

func newService(orm *gorm.DB, logger zerolog.Logger, notificationClient *notification.Notifier) (*service, error) {
	var svcCfg serviceConfig
	_, err := env.UnmarshalFromEnviron(&svcCfg)
	if err != nil {
		return nil, fmt.Errorf("unmarshalConfigFromEnv: %w", err)
	}

	err = orm.AutoMigrate(&Pipeline{}, &PipelineRun{})
	if err != nil {
		return nil, fmt.Errorf("autoMigrate: %w", err)
	}

	logger.Info().Msgf("service config: %+v", svcCfg)
	lokiConn, err := libgrpc.DialWithRetry(svcCfg.LokiSvcAddr, []string{"loki.v1.PipelineService", "loki.v1.ModelService", "loki.v1.AssemblerService"})
	if err != nil {
		return nil, fmt.Errorf("dial loki: %w", err)
	}
	odinConn, err := libgrpc.DialWithRetry(svcCfg.OdinSvcAddr, []string{"heimdall.v1.WorkflowService", "publisher.v1.PublisherService"})
	if err != nil {
		return nil, fmt.Errorf("heimdall grpc.Dial: %w", err)
	}
	flyteClient, err := client.NewFlyteClient(logger)
	if err != nil {
		return nil, fmt.Errorf("flyte client: %w", err)
	}

	return &service{
		cfg:                svcCfg,
		logger:             logger.With().Str("service", "loki.pipeline").Logger(),
		orm:                orm,
		pipelineClient:     pb.NewPipelineServiceClient(lokiConn),
		heimdallClient:     heimdallv1.NewWorkflowServiceClient(odinConn),
		flyteClient:        flyteClient,
		assemblerClient:    pb.NewAssemblerServiceClient(lokiConn),
		modelClient:        pb.NewModelServiceClient(lokiConn),
		publisherClient:    publisherv1.NewPublisherServiceClient(odinConn),
		notificationClient: notificationClient,
		postPublishClient:  pb.NewPostPublishServiceClient(lokiConn),
		schemaCompiler:     jsonschema.NewCompiler(),
		heimdallStartedBy:  "loki",
	}, nil

}
