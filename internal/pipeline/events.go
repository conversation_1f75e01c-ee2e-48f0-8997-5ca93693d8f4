package pipeline

import (
	"context"
	"fmt"
	"time"

	"github.expedia.biz/gmo-performance-marketing/loki/internal/heimdallstatus"
	pbheimdall "github.expedia.biz/gmo-performance-marketing/pb-go/pkg/argo/heimdall/v1"
	pb "github.expedia.biz/gmo-performance-marketing/pb-go/pkg/loki/v1"
	"github.expedia.biz/gmo-performance-marketing/sem-lib-go/pkg/protoserde"
)

func (s *service) HandleHeimdallStatusUpdate(req heimdallstatus.UpdateHandlerRequest) error {
	resp, err := s.heimdallClient.GetWorkflow(
		context.Background(), &pbheimdall.GetWorkflowRequest{WorkflowId: req.WorkflowId},
	)
	if err != nil {
		return err
	}

	switch resp.Workflow.K8SLabels["app.mtt/component"] {
	case string(RunTagModel):
		err = s.handleHeimdallStatusUpdateForModelRun(req, resp.Workflow.K8SLabels["loki/model-run-id"])
	case string(RunTagAssembler):
		err = s.handleHeimdallStatusUpdateAssemblerRun(req, resp.Workflow.K8SLabels["loki/assembler-run-id"])
	case string(RunTagPostPublish):
		err = s.handleHeimdallStatusUpdatePostPublish(req, resp.Workflow.K8SLabels["loki/post-publish-run-id"])
	default:
		return fmt.Errorf("component label not found")
	}

	return err
}

func (s *service) handleHeimdallStatusUpdateForModelRun(
	req heimdallstatus.UpdateHandlerRequest,
	modelRunId string,
) error {
	getModelRunsResp, err := s.modelClient.GetModelRuns(context.Background(), &pb.GetModelRunsRequest{Id: modelRunId})
	if err != nil {
		s.logger.Error().Err(err).Str("model_run_id", modelRunId).Msg("Failed to fetch model run")
		return err
	}
	modelRun := getModelRunsResp.Runs[0]
	updatedExecContext := modelRun.OrchestratorExecContext

	// Workflow failed
	if req.NewStatus == pbheimdall.Status_STATUS_BAD_REQUEST || req.NewStatus == pbheimdall.Status_STATUS_DEADLINE_EXCEEDED {
		updatedExecContext.ExecutionStatus = pb.OrchestratorExecutionStatus_ORCHESTRATOR_EXECUTION_STATUS_FINISHED
		updatedExecContext.IsError = true
		updatedExecContext.ErrorMessage = req.StatusMessage
		updatedExecContext.FinishedAt = protoserde.ToPbTimestamp(time.Now())

		_, err = s.modelClient.UpdateModelRun(
			context.Background(),
			&pb.UpdateModelRunRequest{
				OrchestratorExecContext: updatedExecContext})
		if err != nil {
			s.logger.Error().Err(err).Str("model_run_id", modelRunId).Msg("Failed to update status of model run")
			return err
		}
	}
	return nil
}

func (s *service) handleHeimdallStatusUpdateAssemblerRun(
	req heimdallstatus.UpdateHandlerRequest,
	assemblerRunId string,
) error {
	getAssemblerRunsResp, err := s.assemblerClient.GetAssemblerRuns(context.Background(), &pb.GetAssemblerRunsRequest{Id: assemblerRunId})
	if err != nil {
		s.logger.Error().Err(err).Str("assembler_run_id", assemblerRunId).Msg("Failed to fetch assembler run")
		return err
	}
	assemblerRun := getAssemblerRunsResp.Runs[0]
	updatedExecContext := assemblerRun.OrchestratorExecContext

	// Workflow failed
	if req.NewStatus == pbheimdall.Status_STATUS_BAD_REQUEST || req.NewStatus == pbheimdall.Status_STATUS_DEADLINE_EXCEEDED {
		updatedExecContext.ExecutionStatus = pb.OrchestratorExecutionStatus_ORCHESTRATOR_EXECUTION_STATUS_FINISHED
		updatedExecContext.IsError = true
		updatedExecContext.ErrorMessage = req.StatusMessage
		updatedExecContext.FinishedAt = protoserde.ToPbTimestamp(time.Now())

		_, err = s.assemblerClient.UpdateAssemblerRun(
			context.Background(),
			&pb.UpdateAssemblerRunRequest{
				OrchestratorExecContext: updatedExecContext})
		if err != nil {
			s.logger.Error().Err(err).Str("assembler_run_id", assemblerRunId).Msg("Failed to update status of assembler run")
			return err
		}
	}
	return nil
}

func (s *service) handleHeimdallStatusUpdatePostPublish(req heimdallstatus.UpdateHandlerRequest, postPublishRunId string) error {
	resp, err := s.postPublishClient.GetPostPublishRuns(context.Background(), &pb.GetPostPublishRunsRequest{Id: postPublishRunId})
	if err != nil {
		s.logger.Error().Err(err).Str("post_publish_id", postPublishRunId).Msg("Failed to fetch post publish run")
		return err
	}
	postPublishRun := resp.Runs[0]
	updatedExecContext := postPublishRun.OrchestratorExecContext

	// Workflow failed
	if req.NewStatus == pbheimdall.Status_STATUS_BAD_REQUEST || req.NewStatus == pbheimdall.Status_STATUS_DEADLINE_EXCEEDED {
		updatedExecContext.ExecutionStatus = pb.OrchestratorExecutionStatus_ORCHESTRATOR_EXECUTION_STATUS_FINISHED
		updatedExecContext.IsError = true
		updatedExecContext.ErrorMessage = req.StatusMessage
		updatedExecContext.FinishedAt = protoserde.ToPbTimestamp(time.Now())

		_, err = s.postPublishClient.UpdatePostPublishRun(
			context.Background(),
			&pb.UpdatePostPublishRunRequest{
				OrchestratorExecContext: updatedExecContext})
		if err != nil {
			s.logger.Error().Err(err).Str("post_publish_id", postPublishRunId).Msg("Failed to update status of post publish run")
			return err
		}
	}
	return nil
}
