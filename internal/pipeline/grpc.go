package pipeline

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/robfig/cron/v3"
	"github.com/rs/zerolog"
	"github.expedia.biz/gmo-performance-marketing/loki/internal/notification"
	heimdallv1 "github.expedia.biz/gmo-performance-marketing/pb-go/pkg/argo/heimdall/v1"
	pbenums "github.expedia.biz/gmo-performance-marketing/pb-go/pkg/enums/v1"
	pb "github.expedia.biz/gmo-performance-marketing/pb-go/pkg/loki/v1"
	publisherv1 "github.expedia.biz/gmo-performance-marketing/pb-go/pkg/publisher/v1"
	"github.expedia.biz/gmo-performance-marketing/sem-lib-go/pkg/collections"
	"github.expedia.biz/gmo-performance-marketing/sem-lib-go/pkg/enums"
	"github.expedia.biz/gmo-performance-marketing/sem-lib-go/pkg/protoserde"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/metadata"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/proto"
)

type PublisherRun string

const (
	PublisherRunNA PublisherRun = "NA"
)

type RunTag string

const (
	RunTagModel       RunTag = "model-run"
	RunTagAssembler   RunTag = "assembler-run"
	RunTagPostPublish RunTag = "post-publish-run"
)

type update struct {
	updatePipelineRunRequest *pb.UpdatePipelineRunRequest
}

type memoizedStatus struct {
	isMemoized       bool
	memoizedModelRun *pb.ModelRun
}

func (s *service) CreatePipeline(
	ctx context.Context, req *pb.CreatePipelineRequest,
) (*pb.CreatePipelineResponse, error) {
	var user string
	if md, ok := metadata.FromIncomingContext(ctx); ok {
		if users := md.Get("user"); len(users) > 0 {
			user = users[0]
		}
	}
	if req.MarketingChannel == pbenums.MarketingChannel_MARKETING_CHANNEL_UNSPECIFIED {
		return nil, status.Error(codes.InvalidArgument, "`marketing_channel` is required")
	}
	if req.Name == "" {
		return nil, status.Error(codes.InvalidArgument, "`name` is required")
	}

	err := s.validatePipeline(req.MarketingChannel, req.ModelConfigs)
	if err != nil {
		return nil, err
	}

	modelConfigs, err := protoserde.MarshalArray(req.ModelConfigs)
	if err != nil {
		return nil, status.Errorf(codes.Internal, "marshalModelConfigs: %v", err)
	}
	assemblerConfig, err := proto.Marshal(req.AssemblerConfig)
	if err != nil {
		return nil, status.Errorf(codes.Internal, "marshalAssemblerConfig: %v", err)
	}
	publishConfig, err := proto.Marshal(req.PublishConfig)
	if err != nil {
		return nil, status.Errorf(codes.Internal, "marshalPublishConfig: %v", err)
	}
	postPublishConfigs, err := protoserde.MarshalArray(req.PostPublishConfigs)
	if err != nil {
		return nil, status.Errorf(codes.Internal, "marshalPostPublishConfigs: %v", err)
	}

	p := &Pipeline{
		MarketingChannel:   int32(req.MarketingChannel),
		Status:             int32(req.Status),
		CronExpression:     req.CronExpression,
		Labels:             req.Labels,
		ContactDls:         req.ContactDls,
		ModelConfigs:       modelConfigs,
		AssemblerConfig:    assemblerConfig,
		PublishConfig:      publishConfig,
		PostPublishConfigs: postPublishConfigs,
		CreatedBy:          user,
		Name:               req.Name,
	}
	if err := s.orm.Create(p).Error; err != nil {
		return nil, status.Errorf(codes.Internal, "createPipelineInDB: %v", err)
	}
	return &pb.CreatePipelineResponse{Id: strconv.FormatUint(p.Id, 10)}, nil
}

func (s *service) GetPipelines(
	_ context.Context, req *pb.GetPipelinesRequest,
) (*pb.GetPipelinesResponse, error) {
	if req.MarketingChannel == pbenums.MarketingChannel_MARKETING_CHANNEL_UNSPECIFIED && req.Id == "" {
		return nil, status.Error(codes.InvalidArgument, "`marketing_channel or pipelineId ` is required")
	}

	query := s.orm
	if req.MarketingChannel != pbenums.MarketingChannel_MARKETING_CHANNEL_UNSPECIFIED {
		query = query.Where("marketing_channel = ?", int32(req.MarketingChannel))
	}
	if req.Id != "" {
		id, err := strconv.ParseUint(req.Id, 10, 64)
		if err != nil {
			return nil, status.Errorf(codes.InvalidArgument, "id: %v is not valid", req.Id)
		}
		query = query.Where("id = ?", id)
	}
	for labelKey, labelValue := range req.Labels {
		query = query.Where("labels->>? = ?", labelKey, labelValue)
	}

	var pipelines []*Pipeline
	err := query.Find(&pipelines).Error
	if err != nil {
		return nil, status.Errorf(codes.Internal, "getPipelinesFromDB: %v", err)
	}
	pbPipelines := make([]*pb.Pipeline, len(pipelines))
	for i, p := range pipelines {
		pbPipeline, err := p.toPb()
		if err != nil {
			return nil, status.Errorf(codes.Internal, "toPb: %v", err)
		}
		pbPipelines[i] = pbPipeline
	}

	return &pb.GetPipelinesResponse{Pipelines: pbPipelines}, nil
}

func (s *service) UpdatePipeline(
	ctx context.Context, req *pb.UpdatePipelineRequest,
) (*pb.UpdatePipelineResponse, error) {
	var user string
	if md, ok := metadata.FromIncomingContext(ctx); ok {
		if users := md.Get("user"); len(users) > 0 {
			user = users[0]
		}
	}
	if req.Id == "" {
		return nil, status.Error(codes.InvalidArgument, "`id` is required")
	}
	id, err := strconv.ParseUint(req.Id, 10, 64)
	if err != nil {
		return nil, status.Errorf(codes.InvalidArgument, "id: %v is not valid", req.Id)
	}

	p := &Pipeline{
		Id:             id,
		Status:         int32(req.Status),
		CronExpression: req.CronExpression,
		Labels:         req.Labels,
		ContactDls:     req.ContactDls,
		UpdatedBy:      user,
		Name:           req.Name,
	}

	var curPipeline Pipeline
	err = s.orm.First(&curPipeline, Pipeline{Id: id}).Error
	if err != nil {
		return nil, status.Errorf(codes.NotFound, "getPipeline: %v", err)
	}

	if len(req.ModelConfigs) > 0 {
		err := s.validatePipeline(pbenums.MarketingChannel(curPipeline.MarketingChannel), req.ModelConfigs)
		if err != nil {
			return nil, err
		}
		p.ModelConfigs, err = protoserde.MarshalArray(req.ModelConfigs)
		if err != nil {
			return nil, status.Errorf(codes.Internal, "marshalModelConfigs: %v", err)
		}
	}
	if req.AssemblerConfig != nil {
		p.AssemblerConfig, err = proto.Marshal(req.AssemblerConfig)
		if err != nil {
			return nil, status.Errorf(codes.Internal, "marshalAssemblerConfig: %v", err)
		}
	}
	if req.PublishConfig != nil {
		p.PublishConfig, err = proto.Marshal(req.PublishConfig)
		if err != nil {
			return nil, status.Errorf(codes.Internal, "marshalPublishConfig: %v", err)
		}
	}
	if len(req.PostPublishConfigs) > 0 {
		p.PostPublishConfigs, err = protoserde.MarshalArray(req.PostPublishConfigs)
		if err != nil {
			return nil, status.Errorf(codes.Internal, "marshalPostPublishConfigs: %v", err)
		}
	}
	if req.LastPipelineRunId != "" {
		lastPipelineRunId, _ := strconv.ParseUint(req.LastPipelineRunId, 10, 64)
		p.LastPipelineRunId = lastPipelineRunId
		p.LastCheckedAt = req.LastCheckedAt.AsTime()
	}

	err = s.orm.Updates(&p).Error
	if err != nil {
		return nil, status.Errorf(codes.Internal, "updatePipelineInDB: %v", err)
	}
	return &pb.UpdatePipelineResponse{}, nil
}

func (s *service) validatePipeline(channel pbenums.MarketingChannel, modelConfig []*pb.PipelineModelConfig) error {
	for _, config := range modelConfig {
		modelResp, err := s.modelClient.GetModels(context.Background(), &pb.GetModelsRequest{
			MarketingChannel: channel,
			Id:               config.ModelId,
		})
		if err != nil {
			return status.Errorf(codes.Internal, "getModels: %v", err)
		}
		if len(modelResp.Models) == 0 {
			return status.Errorf(codes.NotFound, "model %s not supported for marketing channel: %s", config.ModelId, pbenums.MarketingChannel_name[int32(channel)])
		}
		if modelResp.Models[0].RunParametersJsonSchema != "" {
			err = s.schemaCompiler.AddResource(config.ModelId, strings.NewReader(modelResp.Models[0].RunParametersJsonSchema))
			if err != nil {
				return status.Errorf(codes.Internal, "unable to add `run_parameters_json_schema` : %v", err)
			}
		}
		sch, err := s.schemaCompiler.Compile(config.ModelId)
		if err != nil {
			return status.Errorf(codes.Internal, "unable to compile `run_parameters_json_schema` : %v", err)
		}
		var runParameters interface{}
		err = json.Unmarshal([]byte(config.RunParameters), &runParameters)
		if err != nil {
			return status.Errorf(codes.Internal, "unable to unmarshal `run_parameters` : %v", err)
		}
		err = sch.Validate(runParameters)
		if err != nil {
			return status.Errorf(codes.InvalidArgument, "model run parameters doesn't follow the model json schema : %v", err)
		}
	}

	if hasCycle(modelConfig) {
		return status.Errorf(codes.InvalidArgument, "modelconfigs have cyclic dependency")
	}

	return nil
}

// Detects if there’s a cycle in the dependency graph
func hasCycle(configs []*pb.PipelineModelConfig) bool {
	graph := make(map[string][]string)
	visited := make(map[string]bool)
	recStack := make(map[string]bool)

	// Build the graph from the configs
	for _, config := range configs {
		graph[config.ModelId] = config.DependsOn
	}

	// DFS function
	var dfs func(node string) bool
	dfs = func(node string) bool {
		if recStack[node] {
			return true // cycle found
		}
		if visited[node] {
			return false
		}

		visited[node] = true
		recStack[node] = true

		for _, neighbor := range graph[node] {
			if dfs(neighbor) {
				return true
			}
		}

		recStack[node] = false
		return false
	}

	// Check for cycles starting from each node
	for node := range graph {
		if !visited[node] {
			if dfs(node) {
				return true
			}
		}
	}

	return false
}

func (s *service) CreatePipelineRun(
	ctx context.Context, req *pb.CreatePipelineRunRequest,
) (*pb.CreatePipelineRunResponse, error) {
	var user string
	if md, ok := metadata.FromIncomingContext(ctx); ok {
		if users := md.Get("user"); len(users) > 0 {
			user = users[0]
		}
	}
	if req.PipelineId == "" {
		return nil, status.Error(codes.InvalidArgument, "`pipeline_id` is required")
	}
	pipelineId, err := strconv.ParseUint(req.PipelineId, 10, 64)
	if err != nil {
		return nil, status.Errorf(
			codes.InvalidArgument, "pipeline_id: %v is not valid", req.PipelineId,
		)
	}

	pipelineRun := &PipelineRun{
		PipelineId:       pipelineId,
		Status:           int32(pb.PipelineRunStatus_PIPELINE_RUN_STATUS_STARTED),
		MarketingChannel: int32(req.MarketingChannel),
		CreatedBy:        user,
	}
	if err := s.orm.Create(pipelineRun).Error; err != nil {
		return nil, status.Errorf(codes.Internal, "createPipelineRunInDB: %v", err)
	}
	return &pb.CreatePipelineRunResponse{Id: strconv.FormatUint(pipelineRun.Id, 10)}, err
}

func (s *service) GetPipelineRuns(
	_ context.Context, req *pb.GetPipelineRunsRequest,
) (*pb.GetPipelineRunsResponse, error) {

	query := s.orm
	if req.MarketingChannel != pbenums.MarketingChannel_MARKETING_CHANNEL_UNSPECIFIED {
		query = query.Where("marketing_channel = ?", int32(req.MarketingChannel))
	}

	limit, offset := 100, 0
	if req.Offset > 0 {
		offset = int(req.Offset)
	}
	if req.Limit > 0 {
		limit = min(limit, int(req.Limit))
	}
	if req.PipelineRunId != "" {
		pipelineRunId, err := strconv.ParseUint(req.PipelineRunId, 10, 64)
		if err != nil {
			return nil, status.Errorf(
				codes.InvalidArgument, "pipeline_run_id: %v is invalid", req.PipelineRunId,
			)
		}
		query = query.Where("id = ?", pipelineRunId)
	}
	if req.PipelineId != "" {
		pipelineId, err := strconv.ParseUint(req.PipelineId, 10, 64)
		if err != nil {
			return nil, status.Errorf(
				codes.InvalidArgument, "pipeline_id: %v is invalid", req.PipelineId,
			)
		}
		query = query.Where("pipeline_id = ?", pipelineId)
	}
	if req.StartedBefore.IsValid() {
		query = query.Where("started_at <= ?", req.StartedBefore.AsTime())
	}
	if req.StartedAfter.IsValid() {
		query = query.Where("started_at >= ?", req.StartedAfter.AsTime())
	}
	if len(req.Statuses) > 0 {
		query = query.Where("status IN (?)", req.Statuses)
	}

	var pipelineRuns []*PipelineRun
	err := query.Offset(offset).Limit(limit).Find(&pipelineRuns).Error
	if err != nil {
		return nil, status.Errorf(codes.Internal, "getPipelineRunsFromDB: %v", err)
	}

	pbPipelineRuns := make([]*pb.PipelineRun, len(pipelineRuns))
	for i, pipelineRun := range pipelineRuns {
		pbPipelineRun, err := pipelineRun.toPb()
		if err != nil {
			return nil, status.Errorf(codes.Internal, "toPb: %v", err)
		}
		pbPipelineRuns[i] = pbPipelineRun
	}

	return &pb.GetPipelineRunsResponse{PipelineRuns: pbPipelineRuns}, nil
}

func (s *service) UpdatePipelineRun(
	ctx context.Context, req *pb.UpdatePipelineRunRequest,
) (*pb.UpdatePipelineRunResponse, error) {
	var user string
	if md, ok := metadata.FromIncomingContext(ctx); ok {
		if users := md.Get("user"); len(users) > 0 {
			user = users[0]
		}
	}

	if req.RunId == "" {
		return nil, status.Error(codes.InvalidArgument, "`run_id` is required")
	}
	id, err := strconv.ParseUint(req.RunId, 10, 64)
	if err != nil {
		return nil, status.Errorf(codes.InvalidArgument, "run_id: %v is not valid", req.RunId)
	}

	p := &PipelineRun{
		Id:           id,
		Status:       int32(req.Status),
		ErrorMessage: req.ErrorMessage,
		IsError:      req.IsError,
		ApprovedBy:   user,
	}

	if len(req.ModelRuns) > 0 {
		p.ModelRuns, err = protoserde.MarshalArray(req.ModelRuns)
		if err != nil {
			return nil, status.Errorf(codes.Internal, "marshalModelRuns: %v", err)
		}
	}
	if req.AssemblerRun != nil {
		p.AssemblerRun, err = proto.Marshal(req.AssemblerRun)
		if err != nil {
			return nil, status.Errorf(codes.Internal, "marshalAssemblerRun: %v", err)
		}
	}
	if req.PublishRun != nil {
		p.PublishRun, err = proto.Marshal(req.PublishRun)
		if err != nil {
			return nil, status.Errorf(codes.Internal, "marshalPublishRun: %v", err)
		}
	}
	if len(req.PostPublishRuns) > 0 {
		p.PostPublishRuns, err = protoserde.MarshalArray(req.PostPublishRuns)
		if err != nil {
			return nil, status.Errorf(codes.Internal, "marshalPostPublishRuns: %v", err)
		}
	}
	if req.Status == pb.PipelineRunStatus_PIPELINE_RUN_STATUS_FINISHED {
		p.FinishedAt = time.Now()
	}

	err = s.orm.Updates(&p).Error
	if err != nil {
		return nil, status.Errorf(codes.Internal, "updatePipelineRunInDB: %v", err)
	}
	return &pb.UpdatePipelineRunResponse{}, nil
}

func (s *service) RunControlLoop(
	context.Context, *pb.RunControlLoopRequest,
) (*pb.RunControlLoopResponse, error) {

	var pollWg sync.WaitGroup
	for key, _ := range pbenums.MarketingChannel_name {
		if pbenums.MarketingChannel(key) == pbenums.MarketingChannel_MARKETING_CHANNEL_UNSPECIFIED {
			continue
		}
		pollWg.Add(1)
		go s.pollPipelineForChannel(pbenums.MarketingChannel(key), &pollWg)
	}
	pollWg.Wait()

	pipelineRuns, err := s.getNonFinishedPipelineRuns()
	if err != nil {
		return nil, status.Errorf(codes.Internal, "getNonFinishedPipelineRuns: %v", err)
	}

	updateChan := make(chan update, len(pipelineRuns))
	var wg sync.WaitGroup
	for _, pipelineRun := range pipelineRuns {
		wg.Add(1)
		go s.hydratePipelineRun(pipelineRun, &wg, updateChan)
	}
	wg.Wait()
	close(updateChan)

	for update := range updateChan {
		_, err := s.pipelineClient.UpdatePipelineRun(context.Background(), update.updatePipelineRunRequest)
		if err != nil {
			s.logger.Error().Err(err).Msg("updatePipelineRun")
		}
	}
	return &pb.RunControlLoopResponse{}, nil
}

func (s *service) pollPipelineForChannel(channel pbenums.MarketingChannel, pollWg *sync.WaitGroup) {
	defer pollWg.Done()
	resp, err := s.pipelineClient.GetPipelines(context.Background(), &pb.GetPipelinesRequest{MarketingChannel: channel})
	if err != nil {
		s.logger.Error().Err(err).Msgf("getPipelines for channel: %s", pbenums.MarketingChannel_name[int32(channel)])
		return
	}

	pipelines := resp.Pipelines
	for _, pipeline := range pipelines {
		if pipeline.CronExpression == "" || pipeline.Status != pb.PipelineStatus_PIPELINE_STATUS_ENABLED {
			continue
		}
		logger := s.logger.With().Str("pipeline_id", pipeline.Id).Logger()

		logger.Info().Msgf("parsing cron: %s for pipelineId: %s", pipeline.CronExpression, pipeline.Id)
		parsedCron, err := cron.ParseStandard(pipeline.CronExpression)
		if err != nil {
			logger.Error().Msgf("Invalid cron expression: %s", pipeline.CronExpression)
			logger.Error().Msgf("cronParse: %v", err)
			continue
		}

		lastCheckedAt := pipeline.LastCheckedAt.AsTime()
		// cold start case when schedule is new
		if lastCheckedAt.IsZero() {
			lastCheckedAt = time.Now().Add(time.Minute * -1)
		}

		if parsedCron.Next(lastCheckedAt).Before(time.Now()) {
			resp, err := s.pipelineClient.CreatePipelineRun(context.Background(), &pb.CreatePipelineRunRequest{PipelineId: pipeline.Id, MarketingChannel: channel})
			if err != nil {
				logger.Error().Err(err).Msg("CreatePipelineRun")
				continue
			}
			pipeline.LastPipelineRunId = resp.Id
		}
		lastCheckedAt = time.Now().Truncate(time.Minute)
		updatePipelineReq := &pb.UpdatePipelineRequest{Id: pipeline.Id, LastPipelineRunId: pipeline.LastPipelineRunId, LastCheckedAt: protoserde.ToPbTimestamp(lastCheckedAt)}
		_, err = s.pipelineClient.UpdatePipeline(context.Background(), updatePipelineReq)
		if err != nil {
			s.logger.Error().Msgf("updatePipeline: %v", err)
		}
	}
}

func (s *service) getNonFinishedPipelineRuns() ([]*pb.PipelineRun, error) {
	const limit = 100
	pipelineRuns := make([]*pb.PipelineRun, 0)

	fetchPipelineRuns := func(offset int32) (*pb.GetPipelineRunsResponse, error) {
		req := &pb.GetPipelineRunsRequest{
			Statuses: []pb.PipelineRunStatus{pb.PipelineRunStatus_PIPELINE_RUN_STATUS_RUNNING, pb.PipelineRunStatus_PIPELINE_RUN_STATUS_STARTED},
			Limit:    limit,
			Offset:   offset,
		}
		resp, err := s.pipelineClient.GetPipelineRuns(context.Background(), req)
		if err != nil {
			return nil, fmt.Errorf("getPipelineRuns: %v", err)
		}
		return resp, nil
	}

	s.logger.Info().Msgf("Fetching running pipeline runs...")
	for {
		resp, err := fetchPipelineRuns(int32(len(pipelineRuns)))
		if err != nil {
			return nil, err
		}
		pipelineRuns = append(pipelineRuns, resp.PipelineRuns...)
		if len(resp.PipelineRuns) < limit {
			break
		}
	}
	s.logger.Info().Msgf("Found %d running pipeline runs", len(pipelineRuns))

	return pipelineRuns, nil
}

func (s *service) hydratePipelineRun(pipelineRun *pb.PipelineRun, wg *sync.WaitGroup, updateChan chan<- update) {
	defer wg.Done()

	logger := s.logger.With().Str("pipeline_id", pipelineRun.PipelineId).Logger()
	ctx := context.Background()
	updatePipelineRunReq := &pb.UpdatePipelineRunRequest{RunId: pipelineRun.Id}

	// Helper function to send error updates
	sendErrorUpdate := func(errMsg string, err error) {
		logger.Error().Err(err).Msg(errMsg)
		updatePipelineRunReq.Status = pb.PipelineRunStatus_PIPELINE_RUN_STATUS_FINISHED
		updatePipelineRunReq.IsError = true
		updatePipelineRunReq.ErrorMessage = fmt.Sprintf("%s: %v", errMsg, err)
		updateChan <- update{updatePipelineRunRequest: updatePipelineRunReq}
	}

	// Helper function to send status updates
	sendUpdate := func(status pb.PipelineRunStatus) {
		updatePipelineRunReq.Status = status
		updateChan <- update{updatePipelineRunRequest: updatePipelineRunReq}
	}

	// 1. Fetch pipeline details
	logger.Info().Msg("Fetching pipeline details from GetPipelines")
	pipelineResp, err := s.pipelineClient.GetPipelines(ctx, &pb.GetPipelinesRequest{Id: pipelineRun.PipelineId})
	if err != nil {
		logger.Error().Err(err).Msg("Failed to get pipeline details from GetPipelines")
		return
	}
	if len(pipelineResp.Pipelines) == 0 {
		logger.Error().Msg("No pipeline found with the provided pipeline_id")
		return
	}

	pipeline := pipelineResp.GetPipelines()[0]

	// Validate pipeline has model configs
	if len(pipeline.ModelConfigs) == 0 {
		logger.Error().Msg("No model configs found in the pipeline")
		updatePipelineRunReq.Status = pb.PipelineRunStatus_PIPELINE_RUN_STATUS_FINISHED
		updatePipelineRunReq.IsError = true
		updatePipelineRunReq.ErrorMessage = "No model configs found"
		updateChan <- update{updatePipelineRunRequest: updatePipelineRunReq}
		return
	}

	// 2. Handle model runs
	if len(pipelineRun.ModelRuns) < len(pipeline.ModelConfigs) {
		// Create model runs if they don't exist
		logger.Info().Msg("Creating model runs...")
		pipelineModelRuns, err := s.createModelRuns(pipeline, logger, pipelineRun)
		if err != nil {
			sendErrorUpdate("Failed to create model runs", err)
			return
		}
		updatePipelineRunReq.ModelRuns = pipelineModelRuns
		sendUpdate(pb.PipelineRunStatus_PIPELINE_RUN_STATUS_RUNNING)
		return
	}

	// 3. Update and check model runs status
	logger.Info().Msg("Updating status of model runs...")
	if err := s.pollAndUpdateModelWorkflowStatus(pipelineRun.GetModelRuns(), pipeline.MarketingChannel); err != nil {
		sendErrorUpdate("Failed to update status of model runs", err)
		return
	}

	logger.Info().Msg("Checking if model runs are completed")
	isCompleted, err := s.checkModelRunsCompletion(pipelineRun.GetModelRuns(), logger, pipeline.MarketingChannel)
	if err != nil {
		sendErrorUpdate("Failed to check model runs completion", err)
		return
	}
	if !isCompleted {
		logger.Info().Msg("Model runs are not completed yet, awaiting completion")
		return
	}

	// 4. Handle assembler run
	if pipelineRun.AssemblerRun.AssemblerRunId == "" {
		logger.Info().Msg("Assembler run not created, creating assembler run")
		pipelineAssemblerRun, err := s.createAssemblerRun(pipeline.AssemblerConfig, pipeline.MarketingChannel, logger, pipelineRun.Id)
		if err != nil {
			sendErrorUpdate("Failed to create assembler run", err)
			return
		}
		updatePipelineRunReq.AssemblerRun = pipelineAssemblerRun
		updateChan <- update{updatePipelineRunRequest: updatePipelineRunReq}
		return
	}

	// 5. Check assembler run status
	logger.Info().Msg("Checking if assembler run is completed")
	isCompleted, assemblerRun, err := s.checkAssemblerRunCompletion(pipelineRun.AssemblerRun.AssemblerRunId, logger)
	if err != nil {
		sendErrorUpdate("Failed to check assembler run completion", err)
		return
	}
	if !isCompleted {
		logger.Info().Msg("Assembler run not completed yet, awaiting completion")
		return
	}

	// 6. Handle publisher run
	logger.Info().Msg("Checking publisher run creation")
	if pipelineRun.PublishRun.PublisherRunId == "" {
		// Calculate bid statistics
		total_bids, success_bids := int64(0), int64(0)
		for _, output := range assemblerRun.Outputs {
			total_bids += output.SuccessBidsCount + output.ErrorBidsCount
			success_bids += output.SuccessBidsCount
		}

		publisherRun := &pb.PipelinePublishRun{}

		if total_bids > 0 && success_bids == 0 {
			logger.Info().Msg("no success bids found in the pipeline")
			sendErrorUpdate("assemblerRun", fmt.Errorf("all bids failed during assembly, check assembler run logs for more details"))
			return
		}

		// No bids to publish
		if success_bids == 0 {
			logger.Info().Msg("No bids to publish, setting pipeline to done...")
			publisherRun.PublisherRunId = string(PublisherRunNA)
			updatePipelineRunReq.PublishRun = publisherRun
			sendUpdate(pb.PipelineRunStatus_PIPELINE_RUN_STATUS_RUNNING)
			return
		}

		if pipeline.PublishConfig.GetIsDryRun() {
			logger.Info().Msg("dry run is true, skipping creation of publisher run...")
			publisherRun.PublisherRunId = string(PublisherRunNA)
			updatePipelineRunReq.PublishRun = publisherRun
			sendUpdate(pb.PipelineRunStatus_PIPELINE_RUN_STATUS_RUNNING)
			return
		}

		// Check if approval is needed
		needsApproval := pipelineRun.ApprovedBy == "" &&
			(int32(success_bids/total_bids) < pipeline.PublishConfig.AutoPublishCriteria.AssemblerSuccessPercentage ||
				!pipeline.PublishConfig.AutoPublish)

		if needsApproval {
			logger.Info().Msg("Waiting for approval due to insufficient success percentage")
			sendUpdate(pb.PipelineRunStatus_PIPELINE_RUN_STATUS_WAITING_FOR_APPROVAL)
			return
		}

		// Create publisher run
		partnerApi, err := enums.GetMarketingPartnerAPIFromChannel(pipeline.MarketingChannel)
		if err != nil {
			sendErrorUpdate("Error getting marketing partner API from channel", err)
			return
		}

		logger.Info().Msg("Creating publisher run")
		publisherResp, err := s.publisherClient.CreateRun(ctx, &publisherv1.CreateRunRequest{
			MarketingPartnerApi: partnerApi,
			S3Path:              assemblerRun.MarketingEntitiesS3Path,
			StartedBy:           "loki",
			AllowConcurrentRun:  true,
			SkipDedupe:          true,
			TraceId:             pipelineRun.Id,
		})
		if err != nil {
			sendErrorUpdate("Failed to create publisher run", err)
			return
		}

		updatePipelineRunReq.PublishRun = &pb.PipelinePublishRun{PublisherRunId: publisherResp.Id}
		sendUpdate(pb.PipelineRunStatus_PIPELINE_RUN_STATUS_RUNNING)
		return
	}

	// 7. Check publisher run status
	if pipelineRun.PublishRun.PublisherRunId != string(PublisherRunNA) {
		logger.Info().Msg("Fetching publisher run details")
		publisherGetResp, err := s.publisherClient.GetRun(ctx, &publisherv1.GetRunRequest{Id: pipelineRun.PublishRun.PublisherRunId})
		if err != nil {
			logger.Error().Err(err).Msg("Failed to get publisher run details")
			return
		}

		if publisherGetResp.Run.IsError {
			finishedAtStr := publisherGetResp.Run.GetFinishedAt().AsTime().Format(time.RFC3339)
			details := notification.NotificationDetails{
				NotificationTypes: []notification.NotificationType{notification.NotificationTypeEmail},
				TemplateType:      notification.TemplateTypeFailure,
				RunId:             publisherGetResp.Run.GetId(),
				Subject:           "Publisher Run Failure",
				FinishedAt:        finishedAtStr,
				ErrorMessage:      publisherGetResp.Run.GetErrorMessage(),
				WorkflowUrl:       publisherGetResp.Run.GetSplitterWorkflowUrl(),
				Contacts:          []string{},
			}
			err := s.notificationClient.SendNotification(details)
			if err != nil {
				logger.Error().Err(err).Msg("Failed to send email notification")
			}
		}

		if publisherGetResp.Run.RunStatus == publisherv1.RunStatus_RUN_STATUS_FINISHED {
			logger.Info().Msg("Publisher run finished successfully")
			updatePipelineRunReq.PublishRun = &pb.PipelinePublishRun{
				PublisherRunId:       pipelineRun.PublishRun.PublisherRunId,
				FailedBidsByResource: getPublisherStatsByResource(publisherGetResp.Run),
			}
			sendUpdate(pb.PipelineRunStatus_PIPELINE_RUN_STATUS_RUNNING)
			return
		}
	}

	if len(pipeline.GetPostPublishConfigs()) == 0 {
		logger.Info().Msgf("no post publish jobs to run, setting pipeline run to done")
		sendUpdate(pb.PipelineRunStatus_PIPELINE_RUN_STATUS_FINISHED)
		return
	}

	if len(pipelineRun.GetPostPublishRuns()) == 0 {
		postPublishRuns, err := s.createPostPublishRuns(pipeline.PostPublishConfigs, pipeline.MarketingChannel, logger, pipelineRun.PipelineId)
		if err != nil {
			sendErrorUpdate("Error creating post publish runs", err)
			return
		}
		updatePipelineRunReq.PostPublishRuns = postPublishRuns
		sendUpdate(pb.PipelineRunStatus_PIPELINE_RUN_STATUS_RUNNING)
	}

	isCompleted, err = s.checkPostPublishRunsCompletion(pipelineRun.GetPostPublishRuns(), logger)
	if err != nil {
		sendErrorUpdate("Failed to check post publish runs completion", err)
		return
	}
	if !isCompleted {
		logger.Info().Msg("Post publish runs are not completed yet, awaiting completion")
		return
	}

	logger.Info().Msgf("pipeline run is now completed successfully")
	sendUpdate(pb.PipelineRunStatus_PIPELINE_RUN_STATUS_FINISHED)
}

// TODO: if error to queue workflow in heimdall update assembler run here?
func (s *service) createAssemblerRun(pipelineAssemblerConfig *pb.PipelineAssemblerConfig, channel pbenums.MarketingChannel, l zerolog.Logger, pipelineRunId string) (*pb.PipelineAssemblerRun, error) {
	if pipelineAssemblerConfig == nil || pipelineAssemblerConfig.OrchestratorConfig == nil {
		return nil, fmt.Errorf("cannot create assembler run without pipeline assembler config")
	}
	assemblerRunResp, err := s.assemblerClient.CreateAssemblerRun(context.Background(), &pb.CreateAssemblerRunRequest{PipelineRunId: pipelineRunId, RunParameters: pipelineAssemblerConfig.RunParameters})
	if err != nil {
		return nil, fmt.Errorf("create assemblerRun: %w", err)
	}
	labels := map[string]string{
		"app.mtt/name":      "loki",
		"app.mtt/component": string(RunTagAssembler),
		"app.mtt/part-of":   "loki",

		"loki/channel":          enums.GetMarketingChannelString(channel),
		"loki/pipeline-run-id":  pipelineRunId,
		"loki/assembler-run-id": assemblerRunResp.Id,
	}
	parameters := map[string]string{
		"runId": assemblerRunResp.Id,
	}

	//var trackingId string
	switch config := pipelineAssemblerConfig.OrchestratorConfig.Config.(type) {
	case *pb.OrchestratorConfig_ArgoConfig:
		{
			k8sNamePrefix := fmt.Sprintf("loki-assembler-run-%s", enums.GetMarketingChannelString(channel))
			_, err = s.createArgoWorkflow(l, config.ArgoConfig.WorkflowTemplateName, labels, parameters, k8sNamePrefix)
			if err != nil {
				return nil, fmt.Errorf("create workflow: %w", err)
			}
		}
	case *pb.OrchestratorConfig_FlyteConfig:
		return nil, fmt.Errorf("flyte orchestrator configuration is not supported yet")

	default:
		return nil, fmt.Errorf("unknown orchestrator configuration type")
	}

	return &pb.PipelineAssemblerRun{AssemblerRunId: assemblerRunResp.Id}, nil
}

func (s *service) createPostPublishRuns(postPublishConfigs []*pb.PipelinePostPublishConfig, channel pbenums.MarketingChannel, l zerolog.Logger, pipelineRunId string) ([]*pb.PipelinePostPublishRun, error) {

	channelStr := enums.GetMarketingChannelString(channel)
	logger := l.With().
		Str("function", "createPostPublishRuns").
		Str("pipeline_run_id", pipelineRunId).
		Str("channel", channelStr).
		Int("post_publish_config_count", len(postPublishConfigs)).
		Logger()

	logger.Info().Msg("Starting post publish run creation process")
	postPublishRuns := make([]*pb.PipelinePostPublishRun, len(postPublishConfigs))
	for index, config := range postPublishConfigs {
		postPublishResp, err := s.postPublishClient.CreatePostPublishRun(context.Background(), &pb.CreatePostPublishRunRequest{PipelineRunId: pipelineRunId, RunParameters: config.RunParameters})
		if err != nil {
			return nil, fmt.Errorf("create postPublishRun: %w", err)
		}
		labels := map[string]string{
			"app.mtt/name":      "loki",
			"app.mtt/component": string(RunTagPostPublish),
			"app.mtt/part-of":   "loki",

			"loki/channel":             enums.GetMarketingChannelString(channel),
			"loki/pipeline-run-id":     pipelineRunId,
			"loki/post-publish-run-id": postPublishResp.Id,
		}
		parameters := map[string]string{
			"runId": postPublishResp.Id,
		}

		//var trackingId string
		switch config := config.OrchestratorConfig.Config.(type) {
		case *pb.OrchestratorConfig_ArgoConfig:
			{
				k8sNamePrefix := fmt.Sprintf("loki-post-publish-run-%s", enums.GetMarketingChannelString(channel))
				_, err = s.createArgoWorkflow(l, config.ArgoConfig.WorkflowTemplateName, labels, parameters, k8sNamePrefix)
				if err != nil {
					return nil, fmt.Errorf("create workflow: %w", err)
				}
			}
		case *pb.OrchestratorConfig_FlyteConfig:
			return nil, fmt.Errorf("flyte orchestrator configuration is not supported yet")

		default:
			return nil, fmt.Errorf("unknown orchestrator configuration type")
		}

		postPublishRuns[index] = &pb.PipelinePostPublishRun{PostPublishId: postPublishResp.Id}
	}
	return postPublishRuns, nil
}

func (s *service) createModelRuns(pipeline *pb.Pipeline, l zerolog.Logger, pipelineRun *pb.PipelineRun) ([]*pb.PipelineModelRun, error) {
	ctx := context.Background()
	modelConfigs := pipeline.ModelConfigs
	channel := pipeline.MarketingChannel
	channelStr := enums.GetMarketingChannelString(channel)

	logger := l.With().
		Str("function", "createModelRuns").
		Str("pipeline_run_id", pipelineRun.Id).
		Str("channel", channelStr).
		Int("model_config_count", len(modelConfigs)).
		Logger()
	logger.Info().Msg("Starting model run creation process")

	// Track completed model IDs
	completedModelIds := collections.Set[string]{}
	for _, modelRun := range pipelineRun.ModelRuns {
		runResp, err := s.modelClient.GetModelRuns(ctx, &pb.GetModelRunsRequest{Id: modelRun.ModelRunId})
		if err != nil {
			logger.Error().Err(err).Msg("Failed to fetch model run details")
			return nil, fmt.Errorf("fetching model run %s: %w", modelRun.ModelRunId, err)
		}
		if len(runResp.Runs) == 0 {
			return nil, fmt.Errorf("no model run found with id %s", modelRun.ModelRunId)
		}
		run := runResp.Runs[0]
		if run.OrchestratorExecContext.ExecutionStatus == pb.OrchestratorExecutionStatus_ORCHESTRATOR_EXECUTION_STATUS_FINISHED {
			completedModelIds.Add(run.ModelId)
		}
	}

	// Initialize result slice
	modelRuns := pipelineRun.ModelRuns

	for i, config := range modelConfigs {
		if completedModelIds.Contains(config.ModelId) {
			continue
		}
		if !areDependenciesMet(config.DependsOn, completedModelIds) {
			continue
		}

		modelLogger := logger.With().Str("model_id", config.ModelId).Int("index", i).Logger()
		modelLogger.Info().Msg("Dependencies met, processing model configuration")

		modelResp, err := s.modelClient.GetModels(ctx, &pb.GetModelsRequest{
			Id:               config.ModelId,
			MarketingChannel: channel,
		})
		if err != nil || len(modelResp.Models) == 0 {
			modelLogger.Error().Err(err).Msg("Failed to retrieve model")
			return nil, fmt.Errorf("get model %s: %w", config.ModelId, err)
		}

		model := modelResp.Models[0]
		if model.OrchestratorConfig == nil {
			modelLogger.Error().Msg("Missing orchestrator config")
			return nil, fmt.Errorf("model %s missing orchestrator config", model.Id)
		}

		// Memoization check
		memoizedStatus, err := s.getMemoizedStatus(model.Id, config.MemoizationConfig)
		if err != nil {
			modelLogger.Error().Err(err).Msg("Memoization check failed")
			return nil, fmt.Errorf("memoization for model %s: %w", model.Id, err)
		}

		var modelRunId string
		if memoizedStatus.isMemoized {
			modelRunId = memoizedStatus.memoizedModelRun.ModelId
			modelLogger.Info().Str("memoized_run_id", modelRunId).Msg("Using memoized run")
			completedModelIds.Add(model.Id)
		} else {
			runResp, err := s.modelClient.CreateModelRun(ctx, &pb.CreateModelRunRequest{
				ModelId:       config.ModelId,
				RunParameters: config.RunParameters,
				PipelineRunId: pipelineRun.Id,
			})
			if err != nil {
				modelLogger.Error().Err(err).Msg("Model run creation failed")
				return nil, fmt.Errorf("create model run for %s: %w", config.ModelId, err)
			}
			modelRunId = runResp.Id
			modelLogger.Info().Str("model_run_id", modelRunId).Msg("Created model run")
		}

		modelRuns = append(modelRuns, &pb.PipelineModelRun{ModelRunId: modelRunId})
	}

	logger.Info().Msg("Model run creation process completed")
	return modelRuns, nil
}

// Helper to check if all dependencies are in the completed set
func areDependenciesMet(dependsOn []string, completed collections.Set[string]) bool {
	for _, id := range dependsOn {
		if !completed.Contains(id) {
			return false
		}
	}
	return true
}

func (s *service) getMemoizedStatus(modelId string, memoizationConfig *pb.PipelineMemoizationConfig) (*memoizedStatus, error) {
	ctx := context.Background()
	logger := s.logger.With().
		Str("function", "getMemoizedStatus").
		Str("model_id", modelId).
		Logger()

	// Default response for when memoization is disabled or no valid run is found
	notMemoizedResponse := &memoizedStatus{isMemoized: false, memoizedModelRun: nil}

	// Check if memoization is enabled
	if !memoizationConfig.IsMemoized {
		logger.Debug().Msg("Memoization is disabled for this model")
		return notMemoizedResponse, nil
	}

	logger.Info().
		Int32("threshold_hours", memoizationConfig.ThresholdHours).
		Msg("Checking for memoizable model runs")

	// Calculate the start of the memoization window
	memoizationWindowStart := time.Now().Add(-time.Duration(memoizationConfig.ThresholdHours) * time.Hour)

	// Query for recent model runs
	prevModelRuns, err := s.modelClient.GetModelRuns(ctx, &pb.GetModelRunsRequest{
		Id:           modelId,
		StartedAfter: protoserde.ToPbTimestamp(memoizationWindowStart),
		Limit:        1,
	})
	if err != nil {
		logger.Error().Err(err).Msg("Failed to fetch model runs for memoization check")
		return nil, fmt.Errorf("failed to fetch model runs for memoization: %w", err)
	}

	// Check if any runs were found
	if len(prevModelRuns.Runs) == 0 {
		logger.Info().
			Time("window_start", memoizationWindowStart).
			Msg("No model runs found within memoization window")
		return notMemoizedResponse, nil
	}

	// Check if the most recent run is completed
	possibleMemoizedRun := prevModelRuns.Runs[0]
	isFinished := possibleMemoizedRun.OrchestratorExecContext.ExecutionStatus ==
		pb.OrchestratorExecutionStatus_ORCHESTRATOR_EXECUTION_STATUS_FINISHED

	if isFinished {
		logger.Info().
			Str("memoized_run_id", possibleMemoizedRun.Id).
			Msg("Valid memoized model run found")
		return &memoizedStatus{
			isMemoized:       true,
			memoizedModelRun: possibleMemoizedRun,
		}, nil
	}

	logger.Info().
		Str("run_id", possibleMemoizedRun.Id).
		Str("status", possibleMemoizedRun.OrchestratorExecContext.ExecutionStatus.String()).
		Msg("Found model run is not in FINISHED state, cannot use for memoization")

	return notMemoizedResponse, nil
}

func (s *service) checkAssemblerRunCompletion(runId string, logger zerolog.Logger) (bool, *pb.AssemblerRun, error) {
	ctx := context.Background()
	logger = logger.With().Str("function", "checkAssemblerRunCompletion").Str("assembler_run_id", runId).Logger()

	// Get assembler run details
	resp, err := s.assemblerClient.GetAssemblerRuns(ctx, &pb.GetAssemblerRunsRequest{Id: runId})
	if err != nil {
		logger.Error().Err(err).Msg("Failed to get assembler runs")
		return false, nil, fmt.Errorf("failed to get assembler run details: %w", err)
	}

	// Validate response
	if len(resp.Runs) == 0 {
		logger.Error().Msg("No assembler runs found with the provided ID")
		return false, nil, fmt.Errorf("no assembler runs found for ID: %s", runId)
	}

	assemblerRun := resp.Runs[0]

	// Check for errors
	if assemblerRun.OrchestratorExecContext.IsError {
		errMsg := assemblerRun.OrchestratorExecContext.ErrorMessage
		finishedAtStr := assemblerRun.OrchestratorExecContext.GetFinishedAt().AsTime().Format(time.RFC3339)
		details := notification.NotificationDetails{
			NotificationTypes: []notification.NotificationType{notification.NotificationTypeEmail},
			TemplateType:      notification.TemplateTypeFailure,
			RunId:             assemblerRun.GetPipelineRunId(),
			Subject:           "Assembler Run Failure",
			FinishedAt:        finishedAtStr,
			ErrorMessage:      assemblerRun.OrchestratorExecContext.GetErrorMessage(),
			WorkflowUrl:       assemblerRun.OrchestratorExecContext.GetTrackingUrl(),
			Contacts:          []string{},
		}
		err := s.notificationClient.SendNotification(details)
		if err != nil {
			logger.Error().Err(err).Msg("Failed to send email notification")
		}

		logger.Error().Str("error", errMsg).Msg("Assembler run failed with error")
		return false, nil, fmt.Errorf("assembler run %s failed: %s", runId, errMsg)
	}

	// Check if completed
	isCompleted := assemblerRun.OrchestratorExecContext.ExecutionStatus ==
		pb.OrchestratorExecutionStatus_ORCHESTRATOR_EXECUTION_STATUS_FINISHED

	if isCompleted {
		logger.Info().Msg("Assembler run completed successfully")
	} else {
		logger.Info().Str("status", assemblerRun.OrchestratorExecContext.ExecutionStatus.String()).
			Msg("Assembler run still in progress")
	}

	return isCompleted, assemblerRun, nil
}

func (s *service) checkPostPublishRunsCompletion(pipelinePostPublishRuns []*pb.PipelinePostPublishRun, logger zerolog.Logger) (bool, error) {
	ctx := context.Background()
	logger = logger.With().Str("function", "checkPostPublishRunsCompletion").Logger()
	totalRuns := len(pipelinePostPublishRuns)
	if totalRuns == 0 {
		logger.Warn().Msg("No post publish runs to check")
		return true, nil // No runs means nothing to wait for
	}

	logger.Info().Int("total_runs", totalRuns).Msg("Checking completion status of post publish runs")
	completedRuns := 0

	for i, postPublishRun := range pipelinePostPublishRuns {
		runLogger := logger.With().
			Str("post_publish_run_id", postPublishRun.PostPublishId).
			Int("run_index", i).
			Logger()

		// Get model run details
		resp, err := s.postPublishClient.GetPostPublishRuns(ctx, &pb.GetPostPublishRunsRequest{
			Id: postPublishRun.PostPublishId,
		})
		if err != nil {
			runLogger.Error().Err(err).Msg("Failed to get post publish run details")
			return false, fmt.Errorf("failed to get details for post publish run %s: %w",
				postPublishRun.PostPublishId, err)
		}

		// Validate response
		if len(resp.Runs) == 0 {
			runLogger.Error().Msg("No post publish run found with the provided ID")
			return false, fmt.Errorf("no post publish run found for ID: %s", postPublishRun.PostPublishId)
		}

		run := resp.Runs[0]

		// Check for errors
		if run.OrchestratorExecContext.IsError {
			runLogger.Error().
				Str("error", run.OrchestratorExecContext.ErrorMessage).
				Msg("Post Publish run failed with error")

			finishedAtStr := run.OrchestratorExecContext.GetFinishedAt().AsTime().Format(time.RFC3339)
			details := notification.NotificationDetails{
				NotificationTypes: []notification.NotificationType{notification.NotificationTypeEmail},
				TemplateType:      notification.TemplateTypeFailure,
				RunId:             run.GetPipelineRunId(),
				Subject:           "Post Publish Run Failure",
				FinishedAt:        finishedAtStr,
				ErrorMessage:      run.OrchestratorExecContext.GetErrorMessage(),
				WorkflowUrl:       run.OrchestratorExecContext.GetTrackingUrl(),
				Contacts:          []string{},
			}
			err := s.notificationClient.SendNotification(details)
			if err != nil {
				logger.Error().Err(err).Msg("Failed to send notification")
			}

			return false, fmt.Errorf("post publish run (ID: %s) failed: %s",
				run.PipelineRunId,
				run.OrchestratorExecContext.ErrorMessage)
		}

		// Check if completed
		if run.OrchestratorExecContext.ExecutionStatus ==
			pb.OrchestratorExecutionStatus_ORCHESTRATOR_EXECUTION_STATUS_FINISHED {
			completedRuns++
			runLogger.Info().Msg("post publish run completed successfully")
		} else {
			runLogger.Info().
				Str("status", run.OrchestratorExecContext.ExecutionStatus.String()).
				Msg("post publish run still in progress")
		}
	}
	allCompleted := completedRuns == totalRuns
	logger.Info().
		Int("completed_runs", completedRuns).
		Int("total_runs", totalRuns).
		Bool("all_completed", allCompleted).
		Msg("Post publish runs completion check result")

	return allCompleted, nil
}

func (s *service) checkModelRunsCompletion(pipelineModelRuns []*pb.PipelineModelRun, logger zerolog.Logger, channel pbenums.MarketingChannel) (bool, error) {
	ctx := context.Background()
	logger = logger.With().Str("function", "checkModelRunsCompletion").Logger()

	totalRuns := len(pipelineModelRuns)
	if totalRuns == 0 {
		logger.Warn().Msg("No model runs to check")
		return true, nil // No runs means nothing to wait for
	}

	logger.Info().Int("total_runs", totalRuns).Msg("Checking completion status of model runs")
	completedRuns := 0

	for i, pipelineModelRun := range pipelineModelRuns {
		runLogger := logger.With().
			Str("model_run_id", pipelineModelRun.ModelRunId).
			Int("run_index", i).
			Logger()

		// Get model run details
		getModelRunResp, err := s.modelClient.GetModelRuns(ctx, &pb.GetModelRunsRequest{
			Id: pipelineModelRun.ModelRunId,
		})
		if err != nil {
			runLogger.Error().Err(err).Msg("Failed to get model run details")
			return false, fmt.Errorf("failed to get details for model run %s: %w",
				pipelineModelRun.ModelRunId, err)
		}

		// Validate response
		if len(getModelRunResp.Runs) == 0 {
			runLogger.Error().Msg("No model run found with the provided ID")
			return false, fmt.Errorf("no model run found for ID: %s", pipelineModelRun.ModelRunId)
		}

		modelRun := getModelRunResp.Runs[0]
		getModelResp, err := s.modelClient.GetModels(ctx, &pb.GetModelsRequest{
			Id:               modelRun.ModelId,
			MarketingChannel: channel,
		})

		contacts := getModelResp.Models[0].ContactDls

		// Check for errors
		if modelRun.OrchestratorExecContext.IsError {
			runLogger.Error().
				Str("model_id", modelRun.ModelId).
				Str("error", modelRun.OrchestratorExecContext.ErrorMessage).
				Msg("Model run failed with error")

			finishedAtStr := modelRun.OrchestratorExecContext.GetFinishedAt().AsTime().Format(time.RFC3339)
			details := notification.NotificationDetails{
				NotificationTypes: []notification.NotificationType{notification.NotificationTypeEmail},
				TemplateType:      notification.TemplateTypeFailure,
				RunId:             modelRun.GetModelId(),
				Subject:           "Model Run Failure",
				FinishedAt:        finishedAtStr,
				ErrorMessage:      modelRun.OrchestratorExecContext.GetErrorMessage(),
				WorkflowUrl:       modelRun.OrchestratorExecContext.TrackingUrl,
				Contacts:          contacts,
			}
			err := s.notificationClient.SendNotification(details)
			if err != nil {
				logger.Error().Err(err).Msg("Failed to send notification")
			}
			return false, fmt.Errorf("model run %s (ID: %s) failed: %s",
				modelRun.ModelId, pipelineModelRun.ModelRunId,
				modelRun.OrchestratorExecContext.ErrorMessage)
		}

		// Check if completed
		if modelRun.OrchestratorExecContext.ExecutionStatus ==
			pb.OrchestratorExecutionStatus_ORCHESTRATOR_EXECUTION_STATUS_FINISHED {
			completedRuns++
			finishedAtStr := modelRun.OrchestratorExecContext.GetFinishedAt().AsTime().Format(time.RFC3339)
			details := notification.NotificationDetails{
				NotificationTypes: []notification.NotificationType{notification.NotificationTypeEmail},
				TemplateType:      notification.TemplateTypeSuccess,
				RunId:             modelRun.GetModelId(),
				Subject:           "Model Run Success",
				FinishedAt:        finishedAtStr,
				ErrorMessage:      "",
				WorkflowUrl:       modelRun.OrchestratorExecContext.TrackingUrl,
				Contacts:          contacts,
			}
			err := s.notificationClient.SendNotification(details)
			if err != nil {
				logger.Error().Err(err).Msg("Failed to send notification")
			}
			runLogger.Info().Msg("Model run completed successfully")
		} else {
			runLogger.Info().
				Str("status", modelRun.OrchestratorExecContext.ExecutionStatus.String()).
				Msg("Model run still in progress")
		}
	}

	allCompleted := completedRuns == totalRuns
	logger.Info().
		Int("completed_runs", completedRuns).
		Int("total_runs", totalRuns).
		Bool("all_completed", allCompleted).
		Msg("Model runs completion check result")

	return allCompleted, nil
}

func getPublisherStatsByResource(publisherRun *publisherv1.Run) map[string]int64 {
	failedBidsByResources := make(map[string]int64)

	// Early return if no account runs
	if len(publisherRun.AccountRuns) == 0 {
		return failedBidsByResources
	}

	// Aggregate failed entities across all account runs
	for _, accountRun := range publisherRun.AccountRuns {
		failedEntitiesByResource := accountRun.GetFailedEntitiesByResource()

		// Merge the failed entities counts into the aggregated map
		for resourceName, failedCount := range failedEntitiesByResource {
			failedBidsByResources[resourceName] += failedCount
		}
	}

	return failedBidsByResources
}

func (s *service) pollAndUpdateModelWorkflowStatus(pipelineModelRuns []*pb.PipelineModelRun, channel pbenums.MarketingChannel) error {
	ctx := context.Background()
	logger := s.logger.With().Str("function", "pollAndUpdateModelWorkflowStatus").Logger()

	if len(pipelineModelRuns) == 0 {
		logger.Info().Msg("No model runs to poll")
		return nil
	}

	logger.Info().Int("run_count", len(pipelineModelRuns)).Msg("Polling model workflow status")

	for i, pipelineModelRun := range pipelineModelRuns {
		runLogger := logger.With().
			Str("model_run_id", pipelineModelRun.ModelRunId).
			Int("index", i).
			Logger()

		// Get model run details
		getModelRunResp, err := s.modelClient.GetModelRuns(ctx, &pb.GetModelRunsRequest{
			Id: pipelineModelRun.ModelRunId,
		})
		if err != nil {
			runLogger.Error().Err(err).Msg("Failed to get model run details")
			return fmt.Errorf("failed to get details for model run %s: %w",
				pipelineModelRun.ModelRunId, err)
		}

		// Validate response
		if len(getModelRunResp.Runs) == 0 {
			runLogger.Error().Msg("No model run found with the provided ID")
			return fmt.Errorf("no model run found for ID: %s", pipelineModelRun.ModelRunId)
		}

		modelRun := getModelRunResp.Runs[0]
		runLogger = runLogger.With().Str("model_id", modelRun.ModelId).Logger()

		// Get model details
		getModelResp, err := s.modelClient.GetModels(ctx, &pb.GetModelsRequest{
			Id:               modelRun.ModelId,
			MarketingChannel: channel,
		})
		if err != nil {
			runLogger.Error().Err(err).Msg("Failed to resolve model")
			return fmt.Errorf("failed to resolve model %s: %w", modelRun.ModelId, err)
		}

		if len(getModelResp.Models) == 0 {
			runLogger.Error().Msg("No model found with the provided ID")
			return fmt.Errorf("no model found for ID: %s", modelRun.ModelId)
		}

		model := getModelResp.Models[0]
		runLogger = runLogger.With().Str("model_name", model.Name).Logger()

		// Handle different orchestrator types
		switch config := model.OrchestratorConfig.Config.(type) {
		case *pb.OrchestratorConfig_FlyteConfig:
			tracking_url := modelRun.OrchestratorExecContext.TrackingUrl
			tracking_url_split := strings.Split(tracking_url, "/")
			execution_id := tracking_url_split[len(tracking_url_split)-1]
			// Handle Flyte workflow
			resp, err := s.flyteClient.GetFlyteWorkflow(execution_id, config.FlyteConfig.Domain)
			if err != nil {
				runLogger.Error().
					Err(err).
					Str("tracking_url", modelRun.OrchestratorExecContext.TrackingUrl).
					Msg("Failed to fetch Flyte workflow")
				return fmt.Errorf("failed to fetch Flyte workflow %s: %w",
					modelRun.OrchestratorExecContext.TrackingUrl, err)
			}

			updatedExecContext := modelRun.OrchestratorExecContext
			updateNeeded := false

			// Handle workflow error
			if resp.IsError {
				runLogger.Error().
					Str("error", resp.Error).
					Msg("Flyte workflow failed with error")

				updatedExecContext.IsError = true
				updatedExecContext.ErrorMessage = resp.Error
				updatedExecContext.FinishedAt = protoserde.ToPbTimestamp(time.Now())
				updateNeeded = true
			} else if resp.IsDone {
				// Handle workflow completion
				runLogger.Info().Msg("Flyte workflow completed successfully")

				updatedExecContext.ExecutionStatus = pb.OrchestratorExecutionStatus_ORCHESTRATOR_EXECUTION_STATUS_FINISHED
				updatedExecContext.FinishedAt = protoserde.ToPbTimestamp(time.Now())
				updateNeeded = true
			} else if updatedExecContext.ExecutionStatus == pb.OrchestratorExecutionStatus_ORCHESTRATOR_EXECUTION_STATUS_QUEUED {
				// Update status from queued to running
				runLogger.Info().Msg("Flyte workflow is now running")

				updatedExecContext.ExecutionStatus = pb.OrchestratorExecutionStatus_ORCHESTRATOR_EXECUTION_STATUS_RUNNING
				updateNeeded = true
			}

			// Update model run if needed
			if updateNeeded {
				_, err = s.modelClient.UpdateModelRun(ctx, &pb.UpdateModelRunRequest{
					Id:                      modelRun.Id,
					OrchestratorExecContext: updatedExecContext,
				})
				if err != nil {
					runLogger.Error().
						Err(err).
						Msg("Failed to update status of model run")
					return fmt.Errorf("failed to update model run %s: %w", modelRun.Id, err)
				}

				runLogger.Info().
					Str("status", updatedExecContext.ExecutionStatus.String()).
					Bool("is_error", updatedExecContext.IsError).
					Msg("Updated model run status")
			} else {
				runLogger.Info().
					Str("status", updatedExecContext.ExecutionStatus.String()).
					Msg("No status update needed for model run")
			}

		case *pb.OrchestratorConfig_ArgoConfig:
			// Heimdall callback manages status updates to model run
			runLogger.Info().
				Str("workflow_template", config.ArgoConfig.WorkflowTemplateName).
				Msg("Argo config polling not needed, skipping...")

		default:
			runLogger.Error().Msg("Unknown orchestrator configuration type")
			return fmt.Errorf("unknown orchestrator configuration type for model %s", model.Id)
		}
	}

	logger.Info().Msg("Completed polling model workflow status")
	return nil
}

func (s *service) createArgoWorkflow(
	l zerolog.Logger,
	argoTemplateName string,
	labels map[string]string,
	parameters map[string]string,
	k8sNamePrefix string,
) (string, error) {
	ctx := context.Background()
	logger := l.With().
		Str("function", "createArgoWorkflow").
		Str("template_name", argoTemplateName).
		Str("k8s_name_prefix", k8sNamePrefix).
		Logger()

	logger.Info().Msg("Creating Argo workflow")

	// Prepare workflow request
	workflowRequest := &heimdallv1.QueueWorkflowRequest{
		Workflow: &heimdallv1.Workflow{
			K8SNamePrefix: k8sNamePrefix,
			K8SNamespace:  "loki",
			K8SLabels:     labels,
			ArgoWorkflowTemplate: &heimdallv1.ArgoWorkflowTemplate{
				WorkflowTemplateName: argoTemplateName,
				Parameters:           parameters,
			},
			StartedBy: s.heimdallStartedBy,
			Callback:  &heimdallv1.Callback{Address: s.cfg.HeimdallStatusSvcServer},
		},
	}

	// Queue the workflow
	resp, err := s.heimdallClient.QueueWorkflow(ctx, workflowRequest)
	if err != nil {
		logger.Error().Err(err).Msg("Failed to queue workflow in Heimdall")
		return "", fmt.Errorf("failed to queue workflow in Heimdall: %w", err)
	}

	workflowId := resp.WorkflowId
	logger.Info().
		Str("workflow_id", workflowId).
		Int("parameter_count", len(parameters)).
		Int("label_count", len(labels)).
		Msg("Successfully created Argo workflow")

	return workflowId, nil
}
