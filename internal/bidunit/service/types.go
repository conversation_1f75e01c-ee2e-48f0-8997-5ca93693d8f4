package service

import (
	"fmt"
	"strconv"
	"time"

	"github.com/lib/pq"
	pbenums "github.expedia.biz/gmo-performance-marketing/pb-go/pkg/enums/v1"
	pb "github.expedia.biz/gmo-performance-marketing/pb-go/pkg/loki/v1"
	"github.expedia.biz/gmo-performance-marketing/sem-lib-go/pkg/protoserde"
	"google.golang.org/protobuf/proto"
)

type BidUnit struct {
	Id uint64 `gorm:"primaryKey"`

	Name             string            `gorm:"uniqueIndex:uidx_loki_bid_unit_name_channel"`
	MarketingChannel int32             `gorm:"uniqueIndex:uidx_loki_bid_unit_name_channel"`
	Labels           map[string]string `gorm:"type:json;serializer:json"`
	Status           int32

	CreatedBy string
	UpdatedBy string
	CreatedAt time.Time `gorm:"autoCreateTime"`
	UpdatedAt time.Time `gorm:"autoUpdateTime"`
}

func (BidUnit) TableName() string {
	return "loki.bid_units"
}

func (b BidUnit) toPb() *pb.BidUnit {
	return &pb.BidUnit{
		Id:               strconv.FormatUint(b.Id, 10),
		Name:             b.Name,
		MarketingChannel: pbenums.MarketingChannel(b.MarketingChannel),
		Labels:           b.Labels,
		Status:           pb.BidUnitStatus(b.Status),
		CreatedBy:        b.CreatedBy,
		CreatedAt:        protoserde.ToPbTimestamp(b.CreatedAt),
		UpdatedBy:        b.UpdatedBy,
		UpdatedAt:        protoserde.ToPbTimestamp(b.UpdatedAt),
	}
}

type BidUnitConfig struct {
	Id        uint64 `gorm:"primaryKey"`
	BidUnitId uint64 `gorm:"index:idx_loki_bid_unit_config_bid_unit_id"`
	Type      int32
	Status    int32

	ModelId          uint64         `gorm:"index:idx_loki_bid_unit_config_model_id"`
	ModelRuleIds     pq.StringArray `gorm:"type:text[]"`
	AssemblerRuleIds pq.StringArray `gorm:"type:text[]"`
	ExperimentConfig []byte

	CreatedBy string
	UpdatedBy string
	CreatedAt time.Time `gorm:"autoCreateTime"`
	UpdatedAt time.Time `gorm:"autoUpdateTime"`

	BidUnitRef BidUnit `gorm:"foreignKey:BidUnitId;references:Id;constraint:OnDelete:CASCADE"`
}

func (BidUnitConfig) TableName() string {
	return "loki.bid_unit_configs"
}

func (b BidUnitConfig) toPb() (*pb.BidUnitConfig, error) {
	experimentConfig := &pb.MarketLabExperimentConfig{}
	err := proto.Unmarshal(b.ExperimentConfig, experimentConfig)
	if err != nil {
		return nil, fmt.Errorf("unmarshallExperimentConfig: %w", err)
	}

	return &pb.BidUnitConfig{
		Id:               strconv.FormatUint(b.Id, 10),
		BidUnitId:        strconv.FormatUint(b.BidUnitId, 10),
		Type:             pb.BidUnitConfigType(b.Type),
		Status:           pb.BidUnitConfigStatus(b.Status),
		CreatedBy:        b.CreatedBy,
		CreatedAt:        protoserde.ToPbTimestamp(b.CreatedAt),
		UpdatedAt:        protoserde.ToPbTimestamp(b.UpdatedAt),
		ModelId:          strconv.FormatUint(b.ModelId, 10),
		ModelRuleIds:     b.ModelRuleIds,
		AssemblerRuleIds: b.AssemblerRuleIds,
		ExperimentConfig: experimentConfig,
	}, nil
}
