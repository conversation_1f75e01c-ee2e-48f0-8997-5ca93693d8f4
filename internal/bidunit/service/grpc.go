package service

import (
	"context"
	"fmt"
	"strconv"

	pbenums "github.expedia.biz/gmo-performance-marketing/pb-go/pkg/enums/v1"
	pb "github.expedia.biz/gmo-performance-marketing/pb-go/pkg/loki/v1"
	"github.expedia.biz/gmo-performance-marketing/sem-lib-go/pkg/collections"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/metadata"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/proto"
	"gorm.io/gorm"
)

func (s *service) CreateBidUnit(
	ctx context.Context, req *pb.CreateBidUnitRequest,
) (*pb.CreateBidUnitResponse, error) {
	var user string
	if md, ok := metadata.FromIncomingContext(ctx); ok {
		if users := md.Get("user"); len(users) > 0 {
			user = users[0]
		}
	}
	if req.Name == "" {
		return nil, status.Error(codes.InvalidArgument, "`name` is a required field")
	}
	if req.MarketingChannel == pbenums.MarketingChannel_MARKETING_CHANNEL_UNSPECIFIED {
		return nil, status.Error(codes.InvalidArgument, "`marketing_channel` is a required field")
	}

	bidUnit := &BidUnit{
		Name:             req.Name,
		MarketingChannel: int32(req.MarketingChannel),
		Labels:           req.Labels,
		Status:           int32(req.Status),
		CreatedBy:        user,
	}

	err := s.orm.Create(bidUnit).Error
	if err != nil {
		return nil, status.Errorf(codes.Internal, "createInDB: %v", err)
	}

	return &pb.CreateBidUnitResponse{Id: strconv.FormatUint(bidUnit.Id, 10)}, nil
}

func (s *service) GetBidUnits(
	_ context.Context, req *pb.GetBidUnitsRequest,
) (*pb.GetBidUnitsResponse, error) {
	if req.MarketingChannel == pbenums.MarketingChannel_MARKETING_CHANNEL_UNSPECIFIED {
		return nil, status.Error(codes.InvalidArgument, "`marketing_channel` is a required field")
	}

	limit, offset := 100, 0
	if req.Offset > 0 {
		offset = int(req.Offset)
	}
	if req.Limit > 0 {
		limit = min(limit, int(req.Limit))
	}

	var bidUnits []BidUnit
	query := s.orm.Where("marketing_channel = ?", int32(req.MarketingChannel))

	if req.Id != "" {
		id, err := strconv.ParseUint(req.Id, 10, 64)
		if err != nil {
			return nil, status.Errorf(codes.InvalidArgument, "id: %v is not valid", req.Id)
		}
		query = query.Where("id = ?", id)
	}
	if len(req.Names) > 0 {
		query = query.Where("name IN ?", req.Names)
	}
	if req.Status != pb.BidUnitStatus_BID_UNIT_STATUS_UNSPECIFIED {
		query = query.Where("status = ?", int32(req.Status))
	}
	for labelKey, labelValue := range req.Labels {
		query = query.Where("labels->>? = ?", labelKey, labelValue)
	}

	err := query.Offset(offset).Limit(limit).Find(&bidUnits).Error
	if err != nil {
		return nil, status.Errorf(codes.Internal, "getFromDB: %v", err)
	}

	pbBidUnits := make([]*pb.BidUnit, len(bidUnits))
	for index, bidUnit := range bidUnits {
		pbBidUnits[index] = bidUnit.toPb()
	}

	return &pb.GetBidUnitsResponse{BidUnits: pbBidUnits}, nil
}

func (s *service) UpdateBidUnit(
	ctx context.Context, req *pb.UpdateBidUnitRequest,
) (*pb.UpdateBidUnitResponse, error) {
	var user string
	if md, ok := metadata.FromIncomingContext(ctx); ok {
		if users := md.Get("user"); len(users) > 0 {
			user = users[0]
		}
	}
	if req.Id == "" {
		return nil, status.Error(codes.InvalidArgument, "`id` is a required field")
	}

	id, err := strconv.ParseUint(req.Id, 10, 64)
	if err != nil {
		return nil, status.Errorf(codes.InvalidArgument, "id: %v is not valid", req.Id)
	}

	err = s.orm.Transaction(func(tx *gorm.DB) error {
		err = s.orm.Updates(&BidUnit{
			Id:        id,
			Labels:    req.Labels,
			Status:    int32(req.Status),
			UpdatedBy: user,
			Name:      req.Name,
		}).Error

		if err != nil {
			return fmt.Errorf("updateInDB: %v", err)
		}

		if req.Status != pb.BidUnitStatus_BID_UNIT_STATUS_DISABLED {
			return nil
		}

		// disable all bid unit configs if the bid unit disabled
		var bidUnitConfig BidUnitConfig
		err = s.orm.Model(&bidUnitConfig).Where("bid_unit_id = ?", id).Update("status", int32(pb.BidUnitConfigStatus_BID_UNIT_CONFIG_STATUS_DISABLED)).Error
		if err != nil {
			return fmt.Errorf("updateBidUnitConfigs: %v", err)
		}
		return nil
	})

	if err != nil {
		return nil, status.Errorf(codes.Internal, "updateInDB: %v", err)
	}

	return &pb.UpdateBidUnitResponse{}, nil
}

func (s *service) CreateBidUnitConfig(
	ctx context.Context, req *pb.CreateBidUnitConfigRequest,
) (*pb.CreateBidUnitConfigResponse, error) {
	var err error
	var bidUnitId uint64
	var modelId uint64
	var user string
	if md, ok := metadata.FromIncomingContext(ctx); ok {
		if users := md.Get("user"); len(users) > 0 {
			user = users[0]
		}
	}
	if req.BidUnitId == "" {
		return nil, status.Error(codes.InvalidArgument, "`bid_unit_id` is a required field")
	} else {
		bidUnitId, err = strconv.ParseUint(req.BidUnitId, 10, 64)
		if err != nil {
			return nil, status.Errorf(
				codes.InvalidArgument, "`bid_unit_id`: %v is not valid", req.BidUnitId,
			)
		}
	}
	if req.ModelId == "" {
		return nil, status.Error(codes.InvalidArgument, "`model_id` is a required field")
	} else {
		modelId, err = strconv.ParseUint(req.ModelId, 10, 64)
		if err != nil {
			return nil, status.Errorf(
				codes.InvalidArgument, "`model_id`: %v is not valid", req.ModelId,
			)
		}
	}
	switch req.Type {
	case pb.BidUnitConfigType_BID_UNIT_CONFIG_TYPE_UNSPECIFIED:
		return nil, status.Error(codes.InvalidArgument, "`type` is a required field")
	case pb.BidUnitConfigType_BID_UNIT_CONFIG_TYPE_BAU:
		if req.ExperimentConfig != nil && (req.ExperimentConfig.ExperimentId != "" || req.ExperimentConfig.BinId != "") {
			return nil, status.Error(
				codes.InvalidArgument, "`experiment_config` can NOT be set for BAU config type",
			)
		}
		var bauCfgs []BidUnitConfig
		err = s.orm.Where("bid_unit_id = ?", bidUnitId).
			Where("type = ?", int32(pb.BidUnitConfigType_BID_UNIT_CONFIG_TYPE_BAU)).
			Find(&bauCfgs).Error
		if err != nil {
			return nil, status.Errorf(codes.Internal, "checkForExistingBAUConfigInDB: %v", err)
		}
		if len(bauCfgs) != 0 {
			return nil, status.Errorf(
				codes.FailedPrecondition,
				"BAU config already exists for this bid_unit_id: %v", bidUnitId,
			)
		}
	case pb.BidUnitConfigType_BID_UNIT_CONFIG_TYPE_OBSERVE:
		if req.ExperimentConfig.ExperimentId != "" || req.ExperimentConfig.BinId != "" {
			return nil, status.Error(
				codes.InvalidArgument, "`experiment_config` can NOT be set for OBSERVE config type",
			)
		}
	case pb.BidUnitConfigType_BID_UNIT_CONFIG_TYPE_CONTROL,
		pb.BidUnitConfigType_BID_UNIT_CONFIG_TYPE_CHALLENGER:
		if req.ExperimentConfig.ExperimentId == "" || req.ExperimentConfig.BinId == "" {
			return nil, status.Error(
				codes.InvalidArgument,
				"both `experiment_id` & `bin_id` fields in `experiment_config` msg must be set",
			)
		}
	}

	err = s.bidUnitConfigValidations(bidUnitId, req.ModelId, req.ModelRuleIds, req.AssemblerRuleIds)
	if err != nil {
		return nil, err
	}

	experimentConfig, err := proto.Marshal(req.ExperimentConfig)
	if err != nil {
		return nil, status.Errorf(codes.Internal, "marshalExperimentConfig: %v", err)
	}

	bidUnitCfg := &BidUnitConfig{
		BidUnitId:        bidUnitId,
		Type:             int32(req.Type),
		Status:           int32(pb.BidUnitConfigStatus_BID_UNIT_CONFIG_STATUS_ENABLED),
		ModelId:          modelId,
		ModelRuleIds:     req.ModelRuleIds,
		AssemblerRuleIds: req.AssemblerRuleIds,
		ExperimentConfig: experimentConfig,
		CreatedBy:        user,
	}
	err = s.orm.Create(bidUnitCfg).Error
	if err != nil {
		return nil, status.Errorf(codes.Internal, "createInDB: %v", err)
	}

	return &pb.CreateBidUnitConfigResponse{Id: strconv.FormatUint(bidUnitCfg.Id, 10)}, nil
}

func (s *service) GetBidUnitConfigs(
	_ context.Context, req *pb.GetBidUnitConfigsRequest,
) (*pb.GetBidUnitConfigsResponse, error) {
	query := s.orm
	if req.BidUnitId != "" {
		bidUnitId, err := strconv.ParseUint(req.BidUnitId, 10, 64)
		if err != nil {
			return nil, status.Errorf(
				codes.InvalidArgument, "`bid_unit_id`: %v is not valid", req.BidUnitId,
			)
		}
		query = query.Where("bid_unit_id = ?", bidUnitId)
	}

	if req.ModelId != "" {
		modelId, err := strconv.ParseUint(req.ModelId, 10, 64)
		if err != nil {
			return nil, status.Errorf(
				codes.InvalidArgument, "`model_id`: %v is not valid", req.ModelId,
			)
		}
		query = query.Where("model_id = ?", modelId)
	}

	if len(req.Ids) != 0 {
		ids := make([]uint64, len(req.Ids))
		for idx, id := range req.Ids {
			uIntId, err := strconv.ParseUint(id, 10, 64)
			if err != nil {
				return nil, status.Errorf(
					codes.InvalidArgument, "`id`: %s is not valid", id)
			}
			ids[idx] = uIntId
		}
		query = query.Where("id IN (?)", ids)
	}

	var bidUnitCfgs []BidUnitConfig
	err := query.Find(&bidUnitCfgs).Error
	if err != nil {
		return nil, status.Errorf(codes.Internal, "getFromDB: %v", err)
	}

	pbBidUnitCfgs := make([]*pb.BidUnitConfig, len(bidUnitCfgs))
	for index, bidUnitCfg := range bidUnitCfgs {
		b, err := bidUnitCfg.toPb()
		if err != nil {
			return nil, status.Errorf(codes.Internal, "toPb: %v", err)
		}
		pbBidUnitCfgs[index] = b
	}

	return &pb.GetBidUnitConfigsResponse{Configs: pbBidUnitCfgs}, nil
}

func (s *service) UpdateBidUnitConfig(
	_ context.Context, req *pb.UpdateBidUnitConfigRequest,
) (*pb.UpdateBidUnitConfigResponse, error) {
	if req.Id == "" {
		return nil, status.Error(codes.InvalidArgument, "")
	}

	id, err := strconv.ParseUint(req.Id, 10, 64)
	if err != nil {
		return nil, status.Errorf(codes.InvalidArgument, "id: %v is not valid", req.Id)
	}
	err = s.orm.Updates(&BidUnitConfig{
		Id:     id,
		Status: int32(req.Status),
	}).Error
	if err != nil {
		return nil, status.Errorf(codes.Internal, "updateInDB: %v", err)
	}

	return &pb.UpdateBidUnitConfigResponse{}, nil
}

func (s *service) bidUnitConfigValidations(bidUnitId uint64, modelId string, modelRuleIds []string, assemblerRuleIds []string) error {
	var bidUnit BidUnit
	err := s.orm.First(&bidUnit, BidUnit{Id: bidUnitId}).Error
	if err != nil {
		return status.Errorf(codes.InvalidArgument, "bidUnitId: %v is not valid", bidUnitId)
	}

	modelResp, err := s.modelClient.GetModels(context.Background(), &pb.GetModelsRequest{
		Id:               modelId,
		MarketingChannel: pbenums.MarketingChannel(bidUnit.MarketingChannel),
	})
	if err != nil {
		return status.Errorf(codes.Internal, "getModels: %v", err)
	}
	if len(modelResp.Models) == 0 {
		return status.Errorf(codes.InvalidArgument, "modelId: %s doesn not support marketing channel: %s", modelId, pbenums.MarketingChannel_name[bidUnit.MarketingChannel])
	}
	model := modelResp.Models[0]
	schemaIds := collections.Set[string]{}
	schemaIds.AddAll(model.RuleSchemaIds...)
	for _, modelRuleId := range modelRuleIds {
		ruleResp, err := s.ruleClient.GetRules(context.Background(), &pb.GetRulesRequest{
			Id:               modelRuleId,
			MarketingChannel: pbenums.MarketingChannel(bidUnit.MarketingChannel),
		})
		if err != nil {
			return status.Errorf(codes.Internal, "getRules: %v", err)
		}
		if len(ruleResp.Rules) == 0 {
			return status.Errorf(codes.InvalidArgument, "modelRuleId: %s is not supported for this marketing channel: %s", modelRuleId, pbenums.MarketingChannel_name[bidUnit.MarketingChannel])
		}
		if ruleResp.Rules[0].AttachmentType != pb.RuleAttachmentType_RULE_ATTACHMENT_TYPE_MODEL {
			return status.Errorf(codes.InvalidArgument, "modelRuleId: %s attachement type is not model", modelRuleId)
		}
		if !schemaIds.Contains(ruleResp.Rules[0].SchemaId) {
			return status.Errorf(codes.InvalidArgument, "modelRuleId: %s is not supported by this model: %s-%s", modelRuleId, model.Name, model.Version)
		}
	}

	for _, assemblerRuleId := range assemblerRuleIds {
		ruleResp, err := s.ruleClient.GetRules(context.Background(), &pb.GetRulesRequest{
			Id:               assemblerRuleId,
			MarketingChannel: pbenums.MarketingChannel(bidUnit.MarketingChannel),
		})
		if err != nil {
			return status.Errorf(codes.Internal, "getRules: %v", err)
		}
		if len(ruleResp.Rules) == 0 {
			return status.Errorf(codes.InvalidArgument, "assemblerRuleId: %s is not supported for this marketing channel: %s", assemblerRuleId, pbenums.MarketingChannel_name[bidUnit.MarketingChannel])
		}
		if ruleResp.Rules[0].AttachmentType != pb.RuleAttachmentType_RULE_ATTACHMENT_TYPE_ASSEMBLER {
			return status.Errorf(codes.InvalidArgument, "assemblerRuleId: %s attachement type is not assembler", assemblerRuleId)
		}
	}
	return nil
}
