package notification

import (
	"fmt"
	"github.expedia.biz/gmo-performance-marketing/sem-lib-go/pkg/aws"
)

type NotificationType string
type TemplateType string

const (
	NotificationTypeEmail NotificationType = "email"
	NotificationTypeSlack NotificationType = "slack"
)

const (
	TemplateTypeSuccess TemplateType = "success"
	TemplateTypeFailure TemplateType = "failure"
)

const (
	SuccessTemplate = `
		<!DOCTYPE html>
		<html>
		<head>
			<title>Loki Notification</title>
		</head>
		<body>
			<h2>Model Success Notification</h2>
			<p><strong>Id:</strong> %s</p>
			<p><strong>Time:</strong> %s</p>
			<p><strong>Link:</strong> <a href="%s">View Workflow</a></p>
		</body>
		</html>
	`

	FailureTemplate = `
		<!DOCTYPE html>
		<html>
		<head>
			<title>Loki Notification</title>
		</head>
		<body>
			<h2>%s</h2>
			<p><strong>Id:</strong> %s</p>
			<p><strong>Time:</strong> %s</p>
			<p><strong>Reason:</strong> %s</p>
			<p><strong>Link:</strong> <a href="%s">Restart Workflow</a></p>
		</body>
		</html>
	`
)

type Notifier struct {
	awsClient aws.Client
}

func NewNotifier(awsClient aws.Client) *Notifier {
	return &Notifier{
		awsClient: awsClient,
	}
}

type NotificationDetails struct {
	NotificationTypes []NotificationType
	TemplateType      TemplateType
	RunId             string
	Subject           string
	FinishedAt        string
	ErrorMessage      string
	WorkflowUrl       string
	Contacts          []string
}

func (s *Notifier) SendNotification(details NotificationDetails) error {
	for _, notificationType := range details.NotificationTypes {
		switch notificationType {
		case NotificationTypeEmail:
			email := s.buildEmail(details.RunId, details.Subject, details.FinishedAt, details.ErrorMessage, details.WorkflowUrl, details.TemplateType)
			err := s.awsClient.SesSendEmail(email)
			if err != nil {
				return fmt.Errorf("failed to send email: %w", err)
			}
		case NotificationTypeSlack:
		default:
			return fmt.Errorf("unsupported notification type: %s", notificationType)
		}
	}
	return nil
}

func (s *Notifier) buildEmail(workflowName, eventType, timestamp, failureReason, actionLink string, templateType TemplateType) *aws.Email {
	var htmlBody string
	if templateType == TemplateTypeSuccess {
		htmlBody = fmt.Sprintf(SuccessTemplate, workflowName, timestamp, actionLink)
	} else if templateType == TemplateTypeFailure {
		htmlBody = fmt.Sprintf(FailureTemplate, eventType, workflowName, timestamp, failureReason, actionLink)
	}

	return &aws.Email{
		Subject:    fmt.Sprintf("[loki]: %s", eventType),
		HtmlBody:   htmlBody,
		Sender:     "<EMAIL>",        // TODO - read from config after testing
		Recipients: []string{"<EMAIL>"}, // TODO - remove this after testing
	}
}
