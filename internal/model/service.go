package model

import (
	"fmt"

	env "github.com/Netflix/go-env"
	"github.com/rs/zerolog"
	"github.com/santhosh-tekuri/jsonschema/v5"
	"github.expedia.biz/gmo-performance-marketing/loki/internal/client"
	heimdallv1 "github.expedia.biz/gmo-performance-marketing/pb-go/pkg/argo/heimdall/v1"
	pb "github.expedia.biz/gmo-performance-marketing/pb-go/pkg/loki/v1"
	libgrpc "github.expedia.biz/gmo-performance-marketing/sem-lib-go/pkg/grpc"
	"google.golang.org/grpc"
	"gorm.io/gorm"
)

func RegisterService(s grpc.ServiceRegistrar, orm *gorm.DB, logger zerolog.Logger) error {
	svc, err := newService(orm, logger)
	if err != nil {
		return err
	}

	pb.RegisterModelServiceServer(s, svc)
	logger.Info().Msg("registered model Service")
	return nil
}

type ServiceConfig struct {
	LokiSvcAddr             string `env:"LOKI_SERVICE_ADDR,default:localhost=50051"`
	OdinSvcAddr             string `env:"ODIN_SERVICE_ADDR,default:localhost=50051"`
	HeimdallStatusSvcServer string `env:"HEIMDALL_STATUS_SVC_SERVER,default=sem-misc-go.test.marketing.expedia.com:443"`
}

type service struct {
	pb.UnimplementedModelServiceServer

	cfg               ServiceConfig
	orm               *gorm.DB
	logger            zerolog.Logger
	schemaCompiler    *jsonschema.Compiler
	ruleClient        pb.RuleServiceClient
	flyteClient       client.FlyteClient
	heimdallStartedBy string
	heimdallClient    heimdallv1.WorkflowServiceClient
}

func newService(orm *gorm.DB, logger zerolog.Logger) (*service, error) {
	var svcCfg ServiceConfig
	_, err := env.UnmarshalFromEnviron(&svcCfg)
	if err != nil {
		return nil, fmt.Errorf("unmarshalConfigFromEnv: %w", err)
	}

	err = orm.AutoMigrate(&Model{}, &ModelRun{})
	if err != nil {
		return nil, fmt.Errorf("autoMigrate: %w", err)
	}
	lokiConn, err := libgrpc.DialWithRetry(svcCfg.LokiSvcAddr, []string{"loki.v1.RuleService"})
	if err != nil {
		return nil, fmt.Errorf("dial loki: %w", err)
	}
	flyteClient, err := client.NewFlyteClient(logger)
	if err != nil {
		return nil, fmt.Errorf("flyte client: %w", err)
	}
	odinConn, err := libgrpc.DialWithRetry(svcCfg.OdinSvcAddr, []string{"heimdall.v1.WorkflowService", "publisher.v1.PublisherService"})
	if err != nil {
		return nil, fmt.Errorf("heimdall grpc.Dial: %w", err)
	}

	return &service{
		cfg:               svcCfg,
		logger:            logger.With().Str("service", "loki.model").Logger(),
		orm:               orm,
		schemaCompiler:    jsonschema.NewCompiler(),
		ruleClient:        pb.NewRuleServiceClient(lokiConn),
		flyteClient:       flyteClient,
		heimdallStartedBy: "loki",
		heimdallClient:    heimdallv1.NewWorkflowServiceClient(odinConn),
	}, nil
}
