package model

import (
	"fmt"
	"strconv"
	"time"

	"github.com/lib/pq"
	pbenums "github.expedia.biz/gmo-performance-marketing/pb-go/pkg/enums/v1"
	pb "github.expedia.biz/gmo-performance-marketing/pb-go/pkg/loki/v1"
	"github.expedia.biz/gmo-performance-marketing/sem-lib-go/pkg/protoserde"
	"google.golang.org/protobuf/proto"
)

type Model struct {
	Id uint64 `gorm:"primaryKey"`

	Name                    string `gorm:"uniqueIndex:uidx_loki_model_name_version"`
	Version                 string `gorm:"uniqueIndex:uidx_loki_model_name_version"`
	Description             string
	RunParametersJsonSchema string
	MarketingChannels       pq.Int32Array     `gorm:"type:integer[]"`
	ContactDls              pq.StringArray    `gorm:"type:text[]"`
	Labels                  map[string]string `gorm:"type:json;serializer:json"`
	Status                  int32
	RuleSchemaIds           pq.StringArray `gorm:"type:text[]"`
	OrchestratorType        int32
	OrchestratorConfig      []byte

	CreatedBy string
	UpdatedBy string
	CreatedAt time.Time `gorm:"autoCreateTime"`
	UpdatedAt time.Time `gorm:"autoUpdateTime"`
}

func (Model) TableName() string {
	return "loki.models"
}

func (m *Model) toPb() (*pb.Model, error) {
	orchestratorConfig := &pb.OrchestratorConfig{}
	err := proto.Unmarshal(m.OrchestratorConfig, orchestratorConfig)
	if err != nil {
		return nil, fmt.Errorf("unmarshallOrchestratorConfig: %w", err)
	}

	return &pb.Model{
		Id:                      strconv.FormatUint(m.Id, 10),
		Name:                    m.Name,
		Version:                 m.Version,
		Description:             m.Description,
		MarketingChannels:       int32ListToMarketingChannelsEnumList(m.MarketingChannels),
		ContactDls:              m.ContactDls,
		Labels:                  m.Labels,
		Status:                  pb.ModelStatus(m.Status),
		RuleSchemaIds:           m.RuleSchemaIds,
		OrchestratorConfig:      orchestratorConfig,
		CreatedBy:               m.CreatedBy,
		CreatedAt:               protoserde.ToPbTimestamp(m.CreatedAt),
		RunParametersJsonSchema: m.RunParametersJsonSchema,
	}, nil
}

type ModelRun struct {
	Id uint64 `gorm:"primaryKey"`

	ModelId                 uint64
	RunParameters           string
	PipelineRunId           uint64
	Outputs                 []byte
	RuleItems               []byte
	OrchestratorExecContext []byte
	StartedAt               time.Time `gorm:"autoCreateTime"`
	UpdatedAt               time.Time `gorm:"autoUpdateTime"`
	FinishedAt              time.Time
	Labels                  pq.StringArray `gorm:"type:text[]"`
	CreatedBy               string

	ModelRef Model `gorm:"foreignKey:ModelId;references:Id;constraint:OnDelete:CASCADE"`
}

func (ModelRun) TableName() string {
	return "loki.model_runs"
}

func (m *ModelRun) toPb() (*pb.ModelRun, error) {
	modelRunOutputs := make([]*pb.ModelRunOutput, 0)
	err := protoserde.UnmarshalArray(m.Outputs, &modelRunOutputs)
	if err != nil {
		return nil, fmt.Errorf("unmarshalModelRunOutputs: %w", err)
	}
	modelRuleItems := make([]*pb.ModelRuleItem, 0)
	err = protoserde.UnmarshalArray(m.RuleItems, &modelRuleItems)
	if err != nil {
		return nil, fmt.Errorf("unmarshalModelRuleItems: %w", err)
	}

	orchestratorExecContext := &pb.OrchestratorExecContext{}
	err = proto.Unmarshal(m.OrchestratorExecContext, orchestratorExecContext)
	if err != nil {
		return nil, fmt.Errorf("unmarshallOrchestratorExecContext: %w", err)
	}
	orchestratorExecContext.StartedAt = protoserde.ToPbTimestamp(m.StartedAt)
	orchestratorExecContext.FinishedAt = protoserde.ToPbTimestamp(m.FinishedAt)

	return &pb.ModelRun{
		Id:                      strconv.FormatUint(m.Id, 10),
		ModelId:                 strconv.FormatUint(m.ModelId, 10),
		RunParameters:           m.RunParameters,
		PipelineRunId:           strconv.FormatUint(m.PipelineRunId, 10),
		Outputs:                 modelRunOutputs,
		RuleItems:               modelRuleItems,
		OrchestratorExecContext: orchestratorExecContext,
		CreatedBy:               m.CreatedBy,
		Labels:                  m.Labels,
	}, nil
}

func marketingChannelsEnumListToInt32List(marketingChannels []pbenums.MarketingChannel) []int32 {
	result := make([]int32, len(marketingChannels))
	for i, channel := range marketingChannels {
		result[i] = int32(channel)
	}
	return result
}

func int32ListToMarketingChannelsEnumList(marketingChannels []int32) []pbenums.MarketingChannel {
	result := make([]pbenums.MarketingChannel, len(marketingChannels))
	for index, marketingChannel := range marketingChannels {
		result[index] = pbenums.MarketingChannel(marketingChannel)
	}
	return result
}
