package mock

import (
	"fmt"
	"os"
	"strings"

	pbenums "github.expedia.biz/gmo-performance-marketing/pb-go/pkg/enums/v1"
	pb "github.expedia.biz/gmo-performance-marketing/pb-go/pkg/loki/v1"
	"google.golang.org/protobuf/encoding/protojson"
)

type ModelClientImpl struct {
	mockModelRuns map[string]*pb.ModelRun
	mockModels    map[string]*pb.Model
}

func NewModelClient() (*ModelClientImpl, error) {
	mockModelRuns, err := deserializeModelRuns()
	if err != nil {
		return nil, err
	}
	mockModels, err := deserializeModels()
	if err != nil {
		return nil, err
	}

	return &ModelClientImpl{mockModelRuns: mockModelRuns, mockModels: mockModels}, nil
}

func (m *ModelClientImpl) GetModelRuns(runIds []string) ([]*pb.ModelRun, error) {
	var modelRuns []*pb.ModelRun
	for _, runId := range runIds {
		modelRun, ok := m.mockModelRuns[runId]
		if !ok {
			return nil, fmt.Errorf("model run: %s not found", runId)
		}
		modelRuns = append(modelRuns, modelRun)
	}
	return modelRuns, nil
}

func (m *ModelClientImpl) GetModel(modelId string, channel pbenums.MarketingChannel) (*pb.Model, error) {
	model, ok := m.mockModels[modelId]
	if !ok {
		return nil, fmt.Errorf("model %s not found", modelId)
	}
	return model, nil
}

func deserializeModelRuns() (map[string]*pb.ModelRun, error) {
	runs := map[string]*pb.ModelRun{}
	jsonData, err := os.ReadFile("../../../test/data/mock/model_runs.jsonl")
	if err != nil {
		return nil, err
	}
	for _, line := range strings.Split(string(jsonData), "\n") {
		if line == "" {
			continue
		}
		run := &pb.ModelRun{}
		err := protojson.Unmarshal([]byte(line), run)
		if err != nil {
			return nil, err
		}
		runs[run.Id] = run
	}
	return runs, nil
}

func deserializeModels() (map[string]*pb.Model, error) {
	models := map[string]*pb.Model{}
	jsonData, err := os.ReadFile("../../../test/data/mock/models.jsonl")
	if err != nil {
		return nil, err
	}
	for _, line := range strings.Split(string(jsonData), "\n") {
		if line == "" {
			continue
		}
		model := &pb.Model{}
		err := protojson.Unmarshal([]byte(line), model)
		if err != nil {
			return nil, err
		}
		models[model.Id] = model
	}
	return models, nil
}
