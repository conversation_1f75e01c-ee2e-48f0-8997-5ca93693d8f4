package mock

import (
	"fmt"
	"os"
	"strings"

	pb "github.expedia.biz/gmo-performance-marketing/pb-go/pkg/loki/v1"
	"google.golang.org/protobuf/encoding/protojson"
)

type PipelineClientImpl struct {
	mockPipelineRuns map[string]*pb.PipelineRun
}

func NewPipelineClient() (*PipelineClientImpl, error) {
	mockPipelineRuns, err := deserializePipelineRuns()
	if err != nil {
		return nil, err
	}
	return &PipelineClientImpl{mockPipelineRuns: mockPipelineRuns}, nil
}

func (p *PipelineClientImpl) GetPipelineRun(runId string) (*pb.PipelineRun, error) {
	run, ok := p.mockPipelineRuns[runId]
	if !ok {
		return nil, fmt.Errorf("no pipelineRun with id %s found", runId)
	}
	return run, nil
}

func (p *PipelineClientImpl) RunControlLoop() error {
	return nil
}

func (p *PipelineClientImpl) GetPipeline(id string) (*pb.Pipeline, error) {
	return &pb.Pipeline{}, nil
}

func deserializePipelineRuns() (map[string]*pb.PipelineRun, error) {
	runs := map[string]*pb.PipelineRun{}
	jsonData, err := os.ReadFile("../../../test/data/mock/pipeline.jsonl")
	if err != nil {
		return nil, err
	}
	for _, line := range strings.Split(string(jsonData), "\n") {
		if line == "" {
			continue
		}
		run := &pb.PipelineRun{}
		err := protojson.Unmarshal([]byte(line), run)
		if err != nil {
			return nil, err
		}
		runs[run.Id] = run
	}
	return runs, nil
}
