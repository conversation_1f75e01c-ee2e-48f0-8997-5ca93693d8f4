package mock

import (
	"context"
	"fmt"
	"os"
	"strings"

	pbenums "github.expedia.biz/gmo-performance-marketing/pb-go/pkg/enums/v1"
	pb "github.expedia.biz/gmo-performance-marketing/pb-go/pkg/loki/v1"
	"google.golang.org/protobuf/encoding/protojson"
)

type BidUnitModel struct {
	BidUnitId string
	ModelId   string
}

type BidUnitClientImpl struct {
	mockBidUnitConfigs map[BidUnitModel]*pb.BidUnitConfig
}

func NewBidUnitClient() (*BidUnitClientImpl, error) {
	mockBidUnitConfigs, err := deserializeBidUnitConfigs()
	if err != nil {
		return nil, err
	}
	return &BidUnitClientImpl{mockBidUnitConfigs: mockBidUnitConfigs}, nil
}

func (bu *BidUnitClientImpl) CreateBidUnit(ctx context.Context, name string, channel pbenums.MarketingChannel, labels map[string]string, status pb.BidUnitStatus) error {
	return nil
}

func (bu *BidUnitClientImpl) GetBidUnitsByChannelAndLabels(marketingChannel pbenums.MarketingChannel, labels map[string]string) ([]*pb.BidUnit, error) {
	return nil, nil
}

func (bu *BidUnitClientImpl) UpdateBidUnitStatus(ctx context.Context, id string, name string, labels map[string]string, status pb.BidUnitStatus) error {
	return nil
}

func (bu *BidUnitClientImpl) GetBidUnitConfigs(bidUnitId string, modelId string) ([]*pb.BidUnitConfig, error) {
	bidUnitModel := BidUnitModel{bidUnitId, modelId}
	bidUnitConfig, ok := bu.mockBidUnitConfigs[bidUnitModel]
	if !ok {
		return nil, fmt.Errorf("bidUnit %v not found", bidUnitModel)
	}
	return []*pb.BidUnitConfig{bidUnitConfig}, nil
}

func (bu *BidUnitClientImpl) GetBidUnit(bidUnitId string, marketingChannel pbenums.MarketingChannel) (*pb.BidUnit, error) {
	return &pb.BidUnit{}, nil
}

func (bu *BidUnitClientImpl) GetBidUnitConfigsById(bidUnitConfigId string) (*pb.BidUnitConfig, error) {
	return &pb.BidUnitConfig{}, nil
}

func deserializeBidUnitConfigs() (map[BidUnitModel]*pb.BidUnitConfig, error) {
	runs := map[BidUnitModel]*pb.BidUnitConfig{}
	jsonData, err := os.ReadFile("../../../test/data/mock/bid_unit_config.jsonl")
	if err != nil {
		return nil, err
	}
	for _, line := range strings.Split(string(jsonData), "\n") {
		if line == "" {
			continue
		}
		config := &pb.BidUnitConfig{}
		err := protojson.Unmarshal([]byte(line), config)
		if err != nil {
			return nil, err
		}
		bidUnitModel := BidUnitModel{config.BidUnitId, config.ModelId}
		runs[bidUnitModel] = config
	}
	return runs, nil
}
