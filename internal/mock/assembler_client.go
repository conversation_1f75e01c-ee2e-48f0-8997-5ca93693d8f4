package mock

import (
	"fmt"
	"os"
	"strings"

	pb "github.expedia.biz/gmo-performance-marketing/pb-go/pkg/loki/v1"
	"google.golang.org/protobuf/encoding/protojson"
)

type AssemblerClientImpl struct {
	mockRuns map[string]*pb.AssemblerRun
}

func NewAssemblerClient() (*AssemblerClientImpl, error) {
	mockRuns, err := deserializeAssemblerRuns()
	if err != nil {
		return nil, err
	}
	return &AssemblerClientImpl{mockRuns: mockRuns}, nil
}

func (ac *AssemblerClientImpl) GetAssemblerRun(runId string) (*pb.AssemblerRun, error) {
	run, ok := ac.mockRuns[runId]
	if !ok {
		return nil, fmt.Errorf("no run with id %s found", runId)
	}
	return run, nil
}

func (ac *AssemblerClientImpl) UpdateAssemblerRunTracking(runId string, orchestratorContext *pb.OrchestratorExecContext) error {
	return nil
}

func (ac *AssemblerClientImpl) UpdateAssemblerRun(run *pb.AssemblerRun) error {
	return nil
}

func deserializeAssemblerRuns() (map[string]*pb.AssemblerRun, error) {
	runs := map[string]*pb.AssemblerRun{}
	jsonData, err := os.ReadFile("../../../test/data/mock/assembler.jsonl")
	if err != nil {
		return nil, err
	}
	for _, line := range strings.Split(string(jsonData), "\n") {
		if line == "" {
			continue
		}
		run := &pb.AssemblerRun{}
		err := protojson.Unmarshal([]byte(line), run)
		if err != nil {
			return nil, err
		}
		runs[run.Id] = run
	}
	return runs, nil
}
