package postpublish

import (
	"fmt"
	"strconv"
	"time"

	pb "github.expedia.biz/gmo-performance-marketing/pb-go/pkg/loki/v1"
	"github.expedia.biz/gmo-performance-marketing/sem-lib-go/pkg/protoserde"
	"google.golang.org/protobuf/proto"
)

type PostPublishRun struct {
	Id uint64 `gorm:"primaryKey"`

	PipelineRunId           string `gorm:"index:idx_loki_post_publish_run_pipeline_run_id"`
	RunParameters           string
	OrchestratorExecContext []byte

	StartedAt  time.Time `gorm:"autoCreateTime"`
	UpdatedAt  time.Time `gorm:"autoUpdateTime"`
	FinishedAt time.Time
}

func (PostPublishRun) TableName() string {
	return "loki.post_publish_runs"
}

func (a *PostPublishRun) toPb() (*pb.PostPublishRun, error) {
	orchestratorExecContext := &pb.OrchestratorExecContext{}
	err := proto.Unmarshal(a.OrchestratorExecContext, orchestratorExecContext)
	if err != nil {
		return nil, fmt.Errorf("unmarshallOrchestratorExecContext: %w", err)
	}
	orchestratorExecContext.StartedAt = protoserde.ToPbTimestamp(a.StartedAt)
	orchestratorExecContext.FinishedAt = protoserde.ToPbTimestamp(a.FinishedAt)

	return &pb.PostPublishRun{
		Id:                      strconv.FormatUint(a.Id, 10),
		RunParameters:           a.RunParameters,
		PipelineRunId:           a.PipelineRunId,
		OrchestratorExecContext: orchestratorExecContext,
	}, nil
}
