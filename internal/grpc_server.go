package internal

import (
	"context"
	"fmt"
	"net"
	"net/http"

	"github.expedia.biz/gmo-performance-marketing/loki/internal/bidunit/service"
	"github.expedia.biz/gmo-performance-marketing/loki/internal/heimdallstatus"
	"github.expedia.biz/gmo-performance-marketing/loki/internal/notification"
	"github.expedia.biz/gmo-performance-marketing/loki/internal/postpublish"
	"github.expedia.biz/gmo-performance-marketing/sem-lib-go/pkg/aws"

	env "github.com/Netflix/go-env"
	"github.com/fullstorydev/grpcui/standalone"
	"github.com/grpc-ecosystem/go-grpc-middleware/v2/interceptors"
	"github.com/grpc-ecosystem/go-grpc-middleware/v2/interceptors/logging"
	"github.com/grpc-ecosystem/go-grpc-middleware/v2/interceptors/selector"
	prom "github.com/grpc-ecosystem/go-grpc-prometheus"
	"github.com/prometheus/client_golang/prometheus/promhttp"
	"github.com/rs/zerolog"
	assembler "github.expedia.biz/gmo-performance-marketing/loki/internal/assembler/service"
	"github.expedia.biz/gmo-performance-marketing/loki/internal/model"
	"github.expedia.biz/gmo-performance-marketing/loki/internal/pipeline"
	"github.expedia.biz/gmo-performance-marketing/loki/internal/rule"
	grpcutils "github.expedia.biz/gmo-performance-marketing/sem-lib-go/pkg/grpc"
	"google.golang.org/grpc"
	"google.golang.org/grpc/health"
	"google.golang.org/grpc/health/grpc_health_v1"
	"google.golang.org/grpc/reflection"
	"gorm.io/gorm"
)

type ServerConfig struct {
	Port             int  `env:"SERVER_PORT,default=50051"`
	MetricsPort      int  `env:"METRICS_PORT,default=9090"`
	DebugUiPort      int  `env:"DEBUG_UI_PORT,default=8080"`
	EnableReflection bool `env:"SERVER_ENABLE_REFLECTION,default=true"`

	Db struct {
		Host     string `env:"DB_HOST,default=localhost"`
		User     string `env:"DB_USER,default=postgres"`
		Password string `env:"DB_PASSWORD,default=postgres"`
		Name     string `env:"DB_NAME,default=postgres"`
		Port     int32  `env:"DB_PORT,default=5432"`
	}
	Environment string `env:"ENV,default=prod"`
	AWSRegion   string `env:"AWS_REGION,default=us-east-1"`
}

func (c *ServerConfig) DbDsn() string {
	return fmt.Sprintf(
		"host=%v user=%v password=%v dbname=%v port=%v",
		c.Db.Host,
		c.Db.User,
		c.Db.Password,
		c.Db.Name,
		c.Db.Port,
	)
}

func NewServerConfig() (*ServerConfig, error) {
	var cfg ServerConfig

	_, err := env.UnmarshalFromEnviron(&cfg)
	if err != nil {
		return nil, fmt.Errorf("unmarshal from env: %w", err)
	}

	return &cfg, nil
}

type Server struct {
	port             int
	metricsPort      int
	debugUiPort      int
	enableReflection bool

	orm                *gorm.DB
	logger             zerolog.Logger
	notificationClient *notification.Notifier
}

func NewServer(
	port int,
	metricsPort int,
	debugUiPort int,
	enableReflection bool,
	orm *gorm.DB,
	logger zerolog.Logger,
	awsClient aws.Client,
) *Server {
	notificationClient := notification.NewNotifier(awsClient)
	return &Server{
		port:             port,
		metricsPort:      metricsPort,
		debugUiPort:      debugUiPort,
		enableReflection: enableReflection,

		orm:                orm,
		logger:             logger,
		notificationClient: notificationClient,
	}
}

func (s *Server) Serve() error {
	lis, err := net.Listen("tcp", fmt.Sprintf(":%d", s.port))
	if err != nil {
		return fmt.Errorf("listen: %w", err)
	}

	server := grpc.NewServer(
		grpc.ChainUnaryInterceptor(
			selector.UnaryServerInterceptor(
				logging.UnaryServerInterceptor(interceptorLogger(s.logger)),
				selector.MatchFunc(loggerInterceptorSelector),
			),
			prom.UnaryServerInterceptor,
		),
		grpc.ChainStreamInterceptor(
			selector.StreamServerInterceptor(
				logging.StreamServerInterceptor(interceptorLogger(s.logger)),
				selector.MatchFunc(loggerInterceptorSelector),
			),
			prom.StreamServerInterceptor,
		),
	)

	err = s.orm.Exec("CREATE SCHEMA IF NOT EXISTS loki").Error
	if err != nil {
		return fmt.Errorf("createSchema: %w", err)
	}

	// register services
	// health
	grpc_health_v1.RegisterHealthServer(server, health.NewServer())
	// heimdallstatus
	heimdallStatusSvc := heimdallstatus.NewService(s.logger)
	heimdallstatus.RegisterService(server, heimdallStatusSvc)

	err = service.RegisterService(server, s.orm, s.logger)
	if err != nil {
		return fmt.Errorf("registerBidUnitService: %w", err)
	}

	err = model.RegisterService(server, s.orm, s.logger)
	if err != nil {
		return fmt.Errorf("registerModelService: %w", err)
	}

	err = rule.RegisterService(server, s.orm, s.logger)
	if err != nil {
		return fmt.Errorf("registerRuleService: %w", err)
	}

	err = assembler.RegisterService(server, s.orm, s.logger)
	if err != nil {
		return fmt.Errorf("registerAssemblerService: %w", err)
	}
	err = postpublish.RegisterService(server, s.orm, s.logger)
	if err != nil {
		return fmt.Errorf("registerPostPublishService: %w", err)
	}

	err = pipeline.RegisterService(server, s.orm, s.logger, heimdallStatusSvc, s.notificationClient)
	if err != nil {
		return fmt.Errorf("registerPipelineService: %w", err)
	}

	if s.enableReflection {
		reflection.Register(server)
	}

	// Register the gRPC server with prometheus & start the prom http server
	prom.EnableHandlingTimeHistogram()
	prom.Register(server)
	http.Handle("/metrics", promhttp.Handler())
	go func() {
		s.logger.Info().Msgf("starting the metrics server on port %d", s.metricsPort)
		if err := http.ListenAndServe(fmt.Sprintf(":%d", s.metricsPort), nil); err != nil {
			s.logger.Fatal().Err(err).Msg("failed to start the prom server")
		}
	}()

	// Start the debug grpcui server
	go s.serveDebugUi()
	s.logger.Info().Msg(fmt.Sprintf("starting the debug server on port %d", s.debugUiPort))

	// Start the gRPC server
	s.logger.Info().Msg(fmt.Sprintf("starting the gRPC server on port %d", s.port))
	err = server.Serve(lis)
	if err != nil {
		return fmt.Errorf("serve: %w", err)
	}
	return nil
}

func (s *Server) serveDebugUi() {
	ctx := context.Background()
	grpcAddr := fmt.Sprintf("dns:///localhost:%d", s.port)
	cc, err := grpcutils.DialWithRetry(grpcAddr, []string{"grpcui"})
	if err != nil {
		s.logger.Err(err).Msg("failed starting connection for docs ui")
		return
	}
	handler, err := standalone.HandlerViaReflection(ctx, cc, grpcAddr)
	if err != nil {
		s.logger.Err(err).Msg("failed starting connection for docs ui")
		return
	}

	mux := &http.ServeMux{}
	mux.Handle("/docs/", http.StripPrefix("/docs", handler))

	h := http.Server{Addr: fmt.Sprintf(":%d", s.debugUiPort), Handler: mux}
	s.logger.Info().Msgf("server /docs on %d", s.debugUiPort)
	if err := h.ListenAndServe(); err != nil {
		s.logger.Err(err).Msg("unable to start the docs server")
	}
}

// interceptorLogger adapts zerolog logger to interceptor logger.
func interceptorLogger(l zerolog.Logger) logging.Logger {
	return logging.LoggerFunc(
		func(_ context.Context, lvl logging.Level, msg string, fields ...any) {
			log := l.With().Fields(fields).Logger()

			switch lvl {
			case logging.LevelDebug:
				log.Debug().Msg(msg)
			case logging.LevelInfo:
				log.Info().Msg(msg)
			case logging.LevelWarn:
				log.Warn().Msg(msg)
			case logging.LevelError:
				log.Error().Msg(msg)
			default:
				panic(fmt.Sprintf("unknown level %v", lvl))
			}
		})
}

func loggerInterceptorSelector(_ context.Context, c interceptors.CallMeta) bool {
	methodsToSkip := []string{
		"/grpc.health.v1.Health/Check",
		"/grpc.reflection.v1alpha.ServerReflection/ServerReflectionInfo",
	}
	for _, methodToSkip := range methodsToSkip {
		if methodToSkip == c.FullMethod() {
			return false
		}
	}
	return true
}
