package client

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"time"

	"github.com/Netflix/go-env"
	"github.com/hashicorp/go-retryablehttp"
	"github.com/rs/zerolog"
)

type flyteClientConfig struct {
	FlyteSvcAddr string `env:"FLYTE_SERVICE_ADDR,default=http://127.0.0.1"`
}

type FlyteClient interface {
	QueueFlyteWorkflow(workflowName string, runParameters map[string]interface{}, domain string) (*QueueWorkflowResponse, error)
	GetFlyteWorkflow(executionId string, domain string) (*GetWorkflowResponse, error)
}

type FlyteClientImpl struct {
	httpClient *retryablehttp.Client

	cfg    flyteClientConfig
	logger zerolog.Logger
}

type QueueWorkflowResponse struct {
	ExecutionId string `json:"execution_id"`
	TrackingUrl string `json:"tracking_url"`
}

type GetWorkflowResponse struct {
	IsDone    bool   `json:"is_done"`
	Error     string `json:"error"`
	IsError   bool   `json:"is_error"`
	ErrorCode string `json:"error_code"`
}

func NewFlyteClient(logger zerolog.Logger) (*FlyteClientImpl, error) {
	var flyteCfg flyteClientConfig
	_, err := env.UnmarshalFromEnviron(&flyteCfg)
	if err != nil {
		return nil, fmt.Errorf("unmarshalConfigFromEnv: %w", err)
	}
	httpClient := retryablehttp.NewClient()
	httpClient.RetryMax = 3
	httpClient.RetryWaitMin = 1 * time.Second
	httpClient.RetryWaitMax = 5 * time.Second

	return &FlyteClientImpl{
		httpClient: httpClient,
		cfg:        flyteCfg,
		logger:     logger.With().Str("component", "flyte.client").Logger(),
	}, nil
}

func (p *FlyteClientImpl) QueueFlyteWorkflow(workflowName string, runParameters map[string]interface{}, domain string) (*QueueWorkflowResponse, error) {
	postBody := map[string]interface{}{
		"workflow_name": workflowName,
		"inputs":        runParameters,
		"domain":        domain,
	}
	postBodyBytes, _ := json.Marshal(postBody)
	p.logger.Info().Msgf("Queueing Flyte workflow: %s", workflowName)
	resp, err := p.httpClient.Post(p.cfg.FlyteSvcAddr+"/ottoman/run_flyte_workflow", "application/json", bytes.NewBuffer(postBodyBytes))
	if err != nil {
		return nil, fmt.Errorf("error creating Flyte workflow: %w", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("error reading response body: %w", err)
	}

	var workflowResponse QueueWorkflowResponse
	if err := json.Unmarshal(body, &workflowResponse); err != nil {
		return nil, fmt.Errorf("error unmarshalling response body: %w", err)
	}
	return &workflowResponse, nil
}

func (p *FlyteClientImpl) GetFlyteWorkflow(executionId string, domain string) (*GetWorkflowResponse, error) {
	url := fmt.Sprintf("%s/ottoman/get_flyte_workflow?execution_name=%s&domain=%s", p.cfg.FlyteSvcAddr, executionId, domain)
	p.logger.Info().Msgf("Fetching Flyte job: %s", executionId)
	resp, err := p.httpClient.Get(url)
	if err != nil {
		return nil, fmt.Errorf("error fetching Flyte workflow: %w", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("error reading response body: %w", err)
	}

	var workflowResponse GetWorkflowResponse
	if err := json.Unmarshal(body, &workflowResponse); err != nil {
		return nil, fmt.Errorf("error unmarshalling response body: %w", err)
	}
	return &workflowResponse, nil
}
