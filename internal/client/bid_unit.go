package client

import (
	"context"
	"fmt"

	"github.com/rs/zerolog"
	pbenums "github.expedia.biz/gmo-performance-marketing/pb-go/pkg/enums/v1"
	pb "github.expedia.biz/gmo-performance-marketing/pb-go/pkg/loki/v1"
	"github.expedia.biz/gmo-performance-marketing/sem-lib-go/pkg/grpc"
)

type BidUnitClient interface {
	GetBidUnitConfigs(bidUnitId string, modelId string) ([]*pb.BidUnitConfig, error)
	GetBidUnit(bidUnitId string, marketingChannel pbenums.MarketingChannel) (*pb.BidUnit, error)
	GetBidUnitConfigsById(bidUnitConfigId string) (*pb.BidUnitConfig, error)
	CreateBidUnit(ctx context.Context, name string, channel pbenums.MarketingChannel, labels map[string]string, status pb.BidUnitStatus) error
	GetBidUnitsByChannelAndLabels(marketingChannel pbenums.MarketingChannel, labels map[string]string) ([]*pb.BidUnit, error)
	UpdateBidUnitStatus(ctx context.Context, id string, name string, labels map[string]string, status pb.BidUnitStatus) error
}

type BidUnitImpl struct {
	client pb.BidUnitServiceClient

	l zerolog.Logger
}

func NewBidUnitClient(l zerolog.Logger, lokiSvcAddr string) (*BidUnitImpl, error) {
	conn, err := grpc.DialWithRetry(lokiSvcAddr, []string{"loki.v1.BidUnitService"})
	if err != nil {
		return nil, fmt.Errorf("grpc dial loki bid unit service failed: %v", err)
	}
	return &BidUnitImpl{client: pb.NewBidUnitServiceClient(conn), l: l}, nil
}

func (bu *BidUnitImpl) GetBidUnitsByChannelAndLabels(marketingChannel pbenums.MarketingChannel, labels map[string]string) ([]*pb.BidUnit, error) {
	var allBidUnits []*pb.BidUnit
	offset := int32(0)
	batchSize := int32(100)

	for {
		resp, err := bu.client.GetBidUnits(context.Background(), &pb.GetBidUnitsRequest{
			MarketingChannel: marketingChannel,
			Limit:            batchSize,
			Offset:           offset,
			Labels:           labels,
		})
		if err != nil {
			return nil, fmt.Errorf("failed to fetch bid units: %w", err)
		}

		allBidUnits = append(allBidUnits, resp.BidUnits...)

		if len(resp.BidUnits) < int(batchSize) {
			break
		}

		offset += batchSize
	}

	return allBidUnits, nil
}

func (bu *BidUnitImpl) UpdateBidUnitStatus(ctx context.Context, id string, name string, labels map[string]string, status pb.BidUnitStatus) error {
	req := &pb.UpdateBidUnitRequest{
		Id:     id,
		Name:   name,
		Labels: labels,
		Status: status,
	}
	_, err := bu.client.UpdateBidUnit(ctx, req)
	if err != nil {
		return fmt.Errorf("update bid unit failed: %w", err)
	}
	return nil
}

func (bu *BidUnitImpl) CreateBidUnit(ctx context.Context, name string, channel pbenums.MarketingChannel, labels map[string]string, status pb.BidUnitStatus) error {
	req := &pb.CreateBidUnitRequest{
		Name:             name,
		MarketingChannel: channel,
		Labels:           labels,
		Status:           status,
	}

	bu.l.Info().Msgf("create bid unit request: %v", req)
	resp, err := bu.client.CreateBidUnit(ctx, req)
	if err != nil {
		return fmt.Errorf("create bid unit failed: %w", err)
	}
	bu.l.Info().Msgf("create bid unit response: %v", resp)
	return nil
}

func (bu *BidUnitImpl) GetBidUnitConfigsById(bidUnitConfigId string) (*pb.BidUnitConfig, error) {
	req := &pb.GetBidUnitConfigsRequest{
		Ids: []string{bidUnitConfigId},
	}
	resp, err := bu.client.GetBidUnitConfigs(context.Background(), req)
	if err != nil {
		return nil, fmt.Errorf("get bid unit configs failed: %v", err)
	}
	if len(resp.Configs) == 0 {
		return nil, fmt.Errorf("get bid unit configs failed, response has no configs")
	}
	return resp.Configs[0], nil
}

func (bu *BidUnitImpl) GetBidUnit(bidUnitId string, marketingChannel pbenums.MarketingChannel) (*pb.BidUnit, error) {
	req := &pb.GetBidUnitsRequest{Id: bidUnitId, MarketingChannel: marketingChannel}

	bu.l.Info().Msgf("get active bid unit request: %v", req)
	resp, err := bu.client.GetBidUnits(context.Background(), req)
	if err != nil {
		return nil, fmt.Errorf("get active bid units failed: %w", err)
	}
	if len(resp.BidUnits) == 0 {
		return nil, fmt.Errorf("active bid unit not found")
	}
	return resp.BidUnits[0], nil
}

func (bu *BidUnitImpl) GetBidUnitConfigs(bidUnitId string, modelId string) ([]*pb.BidUnitConfig, error) {

	req := &pb.GetBidUnitConfigsRequest{BidUnitId: bidUnitId, ModelId: modelId}

	bu.l.Info().Msgf("get active bid unit config request: %v", req)
	resp, err := bu.client.GetBidUnitConfigs(context.Background(), req)
	if err != nil {
		return nil, fmt.Errorf("get active bid unit configs failed: %w", err)
	}
	if len(resp.Configs) == 0 {
		return nil, fmt.Errorf("get active bid unit config response has no configs")
	}
	return resp.Configs, nil

}
