package client

import (
	"context"
	"fmt"

	"github.com/rs/zerolog"
	notificationv1 "github.expedia.biz/gmo-performance-marketing/pb-go/pkg/notification/v1"
	"github.expedia.biz/gmo-performance-marketing/sem-lib-go/pkg/grpc"
)

type NotificationClient interface {
	SendSlackNotification(notification *notificationv1.Notification, channel string) error
}

type NotificationClientImpl struct {
	client notificationv1.NotificationServiceClient

	l zerolog.Logger
}

func NewNotificationClient(l zerolog.Logger, svcAddress string) (NotificationClient, error) {
	conn, err := grpc.DialWithRetry(svcAddress, []string{"notification.v1.NotificationService"})
	if err != nil {
		return nil, fmt.Errorf("grpc dial err: %v", err)
	}
	return &NotificationClientImpl{client: notificationv1.NewNotificationServiceClient(conn), l: l}, nil
}

func (c *NotificationClientImpl) SendSlackNotification(notification *notificationv1.Notification, channel string) error {
	request := &notificationv1.SendRequest{
		Notification: notification,
		Slack: []*notificationv1.SlackRecipient{
			{
				Recipient: &notificationv1.SlackRecipient_Channel{
					Channel: channel,
				},
			},
		},
	}
	_, err := c.client.Send(context.Background(), request)
	if err != nil {
		return fmt.Errorf("send slack err: %v", err)
	}
	return nil
}
