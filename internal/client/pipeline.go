package client

import (
	"context"
	"fmt"

	"github.com/rs/zerolog"
	pb "github.expedia.biz/gmo-performance-marketing/pb-go/pkg/loki/v1"
	"github.expedia.biz/gmo-performance-marketing/sem-lib-go/pkg/grpc"
)

type PipeLineClient interface {
	GetPipelineRun(runId string) (*pb.PipelineRun, error)
	RunControlLoop() error
	GetPipeline(id string) (*pb.Pipeline, error)
}

type PipelineClientImpl struct {
	client pb.PipelineServiceClient

	l zerolog.Logger
}

func GetNewPipelineClient(l zerolog.Logger, lokiSvcAddress string) (*PipelineClientImpl, error) {
	conn, err := grpc.DialWithRetry(lokiSvcAddress, []string{"loki.v1.PipelineService"})
	if err != nil {
		return nil, fmt.Errorf("grpc dial err: %v", err)
	}
	return &PipelineClientImpl{client: pb.NewPipelineServiceClient(conn), l: l}, nil
}

func (p *PipelineClientImpl) RunControlLoop() error {
	_, err := p.client.RunControlLoop(context.Background(), &pb.RunControlLoopRequest{})
	if err != nil {
		return fmt.Errorf("runControlloop: %w", err)
	}
	return nil
}

func (p *PipelineClientImpl) GetPipelineRun(runId string) (*pb.PipelineRun, error) {
	req := &pb.GetPipelineRunsRequest{PipelineRunId: runId}

	p.l.Info().Msgf("getting pipline run for id: %s", runId)
	resp, err := p.client.GetPipelineRuns(context.Background(), req)
	if err != nil {
		return nil, fmt.Errorf("get pipeline run err: %v", err)
	}
	if len(resp.PipelineRuns) == 0 {
		return nil, fmt.Errorf("get pipeline run resp is empty")
	}
	return resp.PipelineRuns[0], nil
}

func (p *PipelineClientImpl) GetPipeline(id string) (*pb.Pipeline, error) {
	req := &pb.GetPipelinesRequest{Id: id}

	p.l.Info().Msgf("getting pipeline %s", id)
	resp, err := p.client.GetPipelines(context.Background(), req)
	if err != nil {
		return nil, fmt.Errorf("get pipeline err: %v", err)
	}
	if len(resp.Pipelines) == 0 {
		return nil, fmt.Errorf("get pipeline resp is empty")
	}
	return resp.Pipelines[0], nil
}
