package client

import (
	"context"
	"fmt"

	"github.com/rs/zerolog"
	pbenums "github.expedia.biz/gmo-performance-marketing/pb-go/pkg/enums/v1"
	pb "github.expedia.biz/gmo-performance-marketing/pb-go/pkg/loki/v1"
	"github.expedia.biz/gmo-performance-marketing/sem-lib-go/pkg/grpc"
)

type ModelClient interface {
	GetModelRuns(runIds []string) ([]*pb.ModelRun, error)
	GetModel(modelId string, marketingChannel pbenums.MarketingChannel) (*pb.Model, error)
}

type ModelClientImpl struct {
	client pb.ModelServiceClient

	l zerolog.Logger
}

func GetNewModelClient(lokiSvcAddress string, l zerolog.Logger) (*ModelClientImpl, error) {
	conn, err := grpc.DialWithRetry(lokiSvcAddress, []string{"loki.v1.ModelService"})
	if err != nil {
		return nil, fmt.Errorf("grpc dial err: %v", err)
	}
	return &ModelClientImpl{client: pb.NewModelServiceClient(conn), l: l}, nil
}

func (m *ModelClientImpl) GetModelRuns(runIds []string) ([]*pb.ModelRun, error) {
	var modelRuns []*pb.ModelRun

	for _, runId := range runIds {
		resp, err := m.client.GetModelRuns(context.Background(), &pb.GetModelRunsRequest{Id: runId})
		if err != nil {
			return nil, fmt.Errorf("get model runs err: %v", err)
		}
		if len(resp.Runs) == 0 {
			return nil, fmt.Errorf("modelRun : %s not found", runId)
		}
		modelRuns = append(modelRuns, resp.Runs...)
	}
	return modelRuns, nil
}

func (m *ModelClientImpl) GetModel(modelId string, channel pbenums.MarketingChannel) (*pb.Model, error) {
	resp, err := m.client.GetModels(context.Background(), &pb.GetModelsRequest{Id: modelId, MarketingChannel: channel})
	if err != nil {
		return nil, fmt.Errorf("get models err: %v", err)
	}
	if len(resp.Models) == 0 {
		return nil, fmt.Errorf("model not found")
	}
	return resp.Models[0], nil
}
