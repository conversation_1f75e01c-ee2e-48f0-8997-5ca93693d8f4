package client

import (
	"encoding/json"
	"fmt"
	"io"
	"net/http"

	"github.com/rs/zerolog"
)

type Bucket struct {
	Name   string
	S3Path string
}

type Instance struct {
	StartDate string
	EndDate   string
}

type Experiment struct {
	Id   string
	Name string

	Bucket        map[string]Bucket
	Channel       string
	Partner       string
	BucketingUnit string
	Instance      []Instance
}

type MarketLabClient interface {
	GetMarketLabExperimentByName(experimentName string, url string) (*Experiment, error)
}

type MarketLabClientImpl struct {
	httpClient HttpClient

	l zerolog.Logger
}

type HttpClient interface {
	Get(url string) (resp *http.Response, err error)
}

func GetMarketLabClient(l zerolog.Logger) *MarketLabClientImpl {
	return &MarketLabClientImpl{httpClient: http.DefaultClient, l: l}
}

func (c *MarketLabClientImpl) GetMarketLabExperimentByName(experimentName string, url string) (*Experiment, error) {
	experimentsByNameFullpath := fmt.Sprintf("%s/experimentsByName/details/%s", url, experimentName)

	c.l.Info().Msgf("Getting experiment by experiment name: %s", experimentName)
	httpResp, err := c.httpClient.Get(experimentsByNameFullpath)
	if err != nil {
		return nil, fmt.Errorf("error getting marketlab splits by experimentId: %w", err)
	}
	bodyBytes, err := io.ReadAll(httpResp.Body)
	if err != nil {
		return nil, fmt.Errorf("error reading response body: %w", err)
	}
	var experimentInfo map[string]interface{}
	err = json.Unmarshal(bodyBytes, &experimentInfo)
	if err != nil {
		return nil, fmt.Errorf("error unmarshalling response body: %w", err)
	}

	if value, _ := experimentInfo["name"]; value != experimentName {
		return nil, fmt.Errorf("No experiment found with name: %s", experimentName)
	}

	id := experimentInfo["id"].(string)
	channel := experimentInfo["channel"].(string)
	partner := experimentInfo["partner"].(string)
	channelSpecificFieldsString := experimentInfo["channelSpecificFields"].(string)
	var channelSpecificFields map[string]interface{}
	err = json.Unmarshal([]byte(channelSpecificFieldsString), &channelSpecificFields)
	if err != nil {
		return nil, fmt.Errorf("error unmarshalling channel specific fields: %w", err)
	}
	c.l.Info().Msgf("channel specific fields: %v", channelSpecificFields)

	var bucketingUnit string
	if unit, ok := channelSpecificFields["bucketingUnit"]; ok {
		bucketingUnit = unit.(string)
	}

	buckets := map[string]Bucket{}
	instances := make([]Instance, 0)
	for _, bucket := range experimentInfo["buckets"].([]interface{}) {
		bucketMap := bucket.(map[string]interface{})
		buckets[bucketMap["bucket"].(string)] = Bucket{Name: bucketMap["bucket"].(string), S3Path: bucketMap["s3Path"].(string)}
	}
	for _, instance := range experimentInfo["instances"].([]interface{}) {
		instanceMap := instance.(map[string]interface{})
		instances = append(instances, Instance{StartDate: instanceMap["startDate"].(string), EndDate: instanceMap["endDate"].(string)})
	}

	experiment := &Experiment{Bucket: buckets, Instance: instances, Name: experimentName, Id: id, Channel: channel, BucketingUnit: bucketingUnit, Partner: partner}
	return experiment, nil
}
