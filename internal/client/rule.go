package client

import (
	"context"
	"fmt"

	"github.com/rs/zerolog"
	enumsv1 "github.expedia.biz/gmo-performance-marketing/pb-go/pkg/enums/v1"
	pb "github.expedia.biz/gmo-performance-marketing/pb-go/pkg/loki/v1"
	"github.expedia.biz/gmo-performance-marketing/sem-lib-go/pkg/grpc"
)

type RuleClient interface {
	GetRules(ruleIds []string, channel enumsv1.MarketingChannel) (map[string]*pb.Rule, error)
	GetRuleItems(ruleIds []string) (map[string][]*pb.RuleItem, error)
	GetRuleSchemas(ruleSchemaIds []string) (map[string]*pb.RuleSchema, error)
}

type RuleClientImpl struct {
	client pb.RuleServiceClient

	l zerolog.Logger
}

func GetNewRuleClient(logger zerolog.Logger, lokiSvcAddress string) (*RuleClientImpl, error) {
	conn, err := grpc.DialWithRetry(lokiSvcAddress, []string{"loki.v1.RuleService"})
	if err != nil {
		return nil, fmt.Errorf("grpc dial err: %v", err)
	}
	return &RuleClientImpl{client: pb.NewRuleServiceClient(conn), l: logger}, nil
}

func (rc *RuleClientImpl) GetRules(ruleIds []string, channel enumsv1.MarketingChannel) (map[string]*pb.Rule, error) {
	rules := map[string]*pb.Rule{}
	for _, ruleId := range ruleIds {
		rc.l.Info().Msgf("Getting rule %s", ruleId)
		req := &pb.GetRulesRequest{Id: ruleId, MarketingChannel: channel}
		resp, err := rc.client.GetRules(context.Background(), req)
		if err != nil {
			return nil, fmt.Errorf("get rules err: %v", err)
		}
		if len(resp.Rules) == 0 {
			return nil, fmt.Errorf("rule %s not found", ruleId)
		}
		for _, rule := range resp.Rules {
			rules[rule.Id] = rule
		}
	}
	return rules, nil
}

func (rc *RuleClientImpl) GetRuleItems(ruleIds []string) (map[string][]*pb.RuleItem, error) {
	ruleItems := map[string][]*pb.RuleItem{}

	for _, ruleId := range ruleIds {
		req := &pb.GetRuleItemsRequest{RuleId: ruleId}

		resp, err := rc.client.GetRuleItems(context.Background(), req)
		if err != nil {
			return nil, fmt.Errorf("get rule items err: %v", err)
		}
		ruleItems[ruleId] = resp.Items
	}
	return ruleItems, nil
}

func (rc *RuleClientImpl) GetRuleSchemas(ruleSchemaIds []string) (map[string]*pb.RuleSchema, error) {
	ruleSchemas := map[string]*pb.RuleSchema{}

	for _, ruleSchemaId := range ruleSchemaIds {
		req := &pb.GetRuleSchemasRequest{Id: ruleSchemaId}

		resp, err := rc.client.GetRuleSchemas(context.Background(), req)
		if err != nil {
			return nil, fmt.Errorf("get rule schemas err: %v", err)
		}
		if len(resp.Schemas) == 0 {
			return nil, fmt.Errorf("no rule schemas found for schema id: %s", ruleSchemaId)
		}
		ruleSchemas[ruleSchemaId] = resp.Schemas[0]
	}
	return ruleSchemas, nil
}
