package client

import (
	"context"
	"fmt"

	"github.com/rs/zerolog"
	pb "github.expedia.biz/gmo-performance-marketing/pb-go/pkg/loki/v1"
	"github.expedia.biz/gmo-performance-marketing/sem-lib-go/pkg/grpc"
)

type AssemblerClient interface {
	GetAssemblerRun(runId string) (*pb.AssemblerRun, error)
	UpdateAssemblerRunTracking(runId string, orchestrationContext *pb.OrchestratorExecContext) error
	UpdateAssemblerRun(run *pb.AssemblerRun) error
}

type AssemblerClientImpl struct {
	client pb.AssemblerServiceClient

	l zerolog.Logger
}

func NewAssemblerClient(LokiSvcAddress string, l zerolog.Logger) (*AssemblerClientImpl, error) {
	conn, err := grpc.DialWithRetry(LokiSvcAddress, []string{"loki.v1.AssemblerService"})
	if err != nil {
		return nil, fmt.Errorf("grpc dial err: %v", err)
	}
	return &AssemblerClientImpl{client: pb.NewAssemblerServiceClient(conn), l: l}, nil
}

func (acl AssemblerClientImpl) GetAssemblerRun(runId string) (*pb.AssemblerRun, error) {
	req := &pb.GetAssemblerRunsRequest{Id: runId}

	acl.l.Info().Msgf("GetAssemblerRuns req: %v", req)
	resp, err := acl.client.GetAssemblerRuns(context.Background(), req)
	if err != nil {
		return nil, fmt.Errorf("getAssemblerRun: %w", err)
	}
	if len(resp.GetRuns()) <= 0 {
		return nil, fmt.Errorf("getAssemblerRun: no runs found")
	}
	return resp.GetRuns()[0], nil
}

func (acl AssemblerClientImpl) UpdateAssemblerRunTracking(runId string, orchestratorContext *pb.OrchestratorExecContext) error {
	req := &pb.UpdateAssemblerRunRequest{Id: runId, OrchestratorExecContext: orchestratorContext}
	acl.l.Info().Msgf("updating assembler run tracking: %v", req)
	_, err := acl.client.UpdateAssemblerRun(context.Background(), req)
	return err
}

func (acl AssemblerClientImpl) UpdateAssemblerRun(
	run *pb.AssemblerRun,
) error {
	req := &pb.UpdateAssemblerRunRequest{
		Id:                      run.Id,
		Outputs:                 run.Outputs,
		RuleItems:               run.RuleItems,
		MarketingEntitiesS3Path: run.MarketingEntitiesS3Path,
	}
	acl.l.Info().Msgf("updating assembler run outputs: %v", req)
	_, err := acl.client.UpdateAssemblerRun(context.Background(), req)
	return err
}
