package rule

import (
	"strconv"
	"time"

	pbenums "github.expedia.biz/gmo-performance-marketing/pb-go/pkg/enums/v1"
	pb "github.expedia.biz/gmo-performance-marketing/pb-go/pkg/loki/v1"
	"github.expedia.biz/gmo-performance-marketing/sem-lib-go/pkg/protoserde"
)

type RuleSchema struct {
	Id uint64 `gorm:"primaryKey"`

	Name           string            `gorm:"uniqueIndex:uidx_loki_rule_name"`
	Labels         map[string]string `gorm:"type:json;serializer:json"`
	ItemJsonSchema string
	Status         int32
	Description    string

	CreatedBy string
	UpdatedBy string
	CreatedAt time.Time `gorm:"autoCreateTime"`
	UpdatedAt time.Time `gorm:"autoUpdateTime"`
}

func (RuleSchema) TableName() string {
	return "loki.rule_schemas"
}

func (r RuleSchema) toPb() *pb.RuleSchema {
	return &pb.RuleSchema{
		Id:             strconv.FormatUint(r.Id, 10),
		Name:           r.Name,
		Labels:         r.Labels,
		ItemJsonSchema: r.Item<PERSON>sonSchema,
		CreatedBy:      r.<PERSON>,
		CreatedAt:      protoserde.ToPbTimestamp(r.CreatedAt),
		UpdatedBy:      r.UpdatedBy,
		UpdatedAt:      protoserde.ToPbTimestamp(r.UpdatedAt),
		Status:         pb.RuleSchemaStatus(r.Status),
		Description:    r.Description,
	}
}

type Rule struct {
	Id uint64 `gorm:"primaryKey"`

	Name             string `gorm:"uniqueIndex:uidx_loki_rule_name_channel"`
	MarketingChannel int32  `gorm:"uniqueIndex:uidx_loki_rule_name_channel"`

	RuleSchemaId   uint64
	AttachmentType int32
	Status         int32
	Labels         map[string]string `gorm:"type:json;serializer:json"`

	CreatedBy string
	UpdatedBy string
	CreatedAt time.Time `gorm:"autoCreateTime"`
	UpdatedAt time.Time `gorm:"autoUpdateTime"`

	RuleSchemaRef RuleSchema `gorm:"foreignKey:RuleSchemaId;references:Id;constraint:OnDelete:CASCADE"`
}

func (Rule) TableName() string {
	return "loki.rules"
}

func (r Rule) toPb() *pb.Rule {
	return &pb.Rule{
		Id:               strconv.FormatUint(r.Id, 10),
		Name:             r.Name,
		MarketingChannel: pbenums.MarketingChannel(r.MarketingChannel),
		SchemaId:         strconv.FormatUint(r.RuleSchemaId, 10),
		AttachmentType:   pb.RuleAttachmentType(r.AttachmentType),
		Labels:           r.Labels,
		Status:           pb.RuleStatus(r.Status),
		CreatedBy:        r.CreatedBy,
		CreatedAt:        protoserde.ToPbTimestamp(r.CreatedAt),
	}
}

type RuleItem struct {
	Id      uint64 `gorm:"primaryKey"`
	Version uint64 `gorm:"primaryKey;autoIncrement:true"`

	RuleId         uint64 `gorm:"index:idx_loki_rule_item_rule_id"`
	Info           string
	VersionBasedOn uint64
	BusinessCase   string
	Status         int32

	CreatedBy string
	UpdatedBy string
	CreatedAt time.Time `gorm:"autoCreateTime"`
	UpdatedAt time.Time `gorm:"autoUpdateTime"`

	RuleRef Rule `gorm:"foreignKey:RuleId;references:Id;constraint:OnDelete:CASCADE"`
}

func (RuleItem) TableName() string {
	return "loki.rule_items"
}

func (r RuleItem) toPb() *pb.RuleItem {
	return &pb.RuleItem{
		Id:             strconv.FormatUint(r.Id, 10),
		Version:        strconv.FormatUint(r.Version, 10),
		RuleId:         strconv.FormatUint(r.RuleId, 10),
		Info:           r.Info,
		VersionBasedOn: strconv.FormatUint(r.VersionBasedOn, 10),
		BusinessCase:   r.BusinessCase,
		Status:         pb.RuleItemStatus(r.Status),
		CreatedBy:      r.CreatedBy,
		CreatedAt:      protoserde.ToPbTimestamp(r.CreatedAt),
		UpdatedAt:      protoserde.ToPbTimestamp(r.UpdatedAt),
		UpdatedBy:      r.UpdatedBy,
	}
}
