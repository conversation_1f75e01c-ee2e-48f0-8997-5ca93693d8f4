package rule

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"
	"strings"

	pbenums "github.expedia.biz/gmo-performance-marketing/pb-go/pkg/enums/v1"
	pb "github.expedia.biz/gmo-performance-marketing/pb-go/pkg/loki/v1"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/metadata"
	"google.golang.org/grpc/status"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

func (s *service) CreateRuleSchema(
	ctx context.Context, req *pb.CreateRuleSchemaRequest,
) (*pb.CreateRuleSchemaResponse, error) {
	var user string
	if md, ok := metadata.FromIncomingContext(ctx); ok {
		if users := md.Get("user"); len(users) > 0 {
			user = users[0]
		}
	}
	if req.Name == "" {
		return nil, status.Error(codes.InvalidArgument, "`name` is a required field")
	}
	if req.ItemJsonSchema == "" {
		return nil, status.Error(codes.InvalidArgument, "`item_json_schema` is a required field")
	} else {
		s.logger.Info().Msgf("validating the json schema: %s", req.Name)
		err := s.schemaComplier.AddResource(req.Name, strings.NewReader(req.ItemJsonSchema))
		if err != nil {
			return nil, status.Errorf(codes.Internal, "unable to add `item_json_schema` : %v", err)
		}
		_, err = s.schemaComplier.Compile(req.Name)
		if err != nil {
			return nil, status.Errorf(codes.InvalidArgument, "json schema is invalid: %v", err)
		}
	}

	r := &RuleSchema{
		Name:           req.Name,
		Labels:         req.Labels,
		ItemJsonSchema: req.ItemJsonSchema,
		CreatedBy:      user,
		Status:         int32(pb.RuleSchemaStatus_RULE_SCHEMA_STATUS_ENABLED),
		Description:    req.Description,
	}
	err := s.orm.Create(r).Error
	if err != nil {
		return nil, status.Errorf(codes.Internal, "createInDB: %v", err)
	}

	return &pb.CreateRuleSchemaResponse{Id: strconv.FormatUint(r.Id, 10)}, nil
}

func (s *service) GetRuleSchemas(
	_ context.Context, req *pb.GetRuleSchemasRequest,
) (*pb.GetRuleSchemasResponse, error) {
	limit, offset := 100, 0
	if req.Offset > 0 {
		offset = int(req.Offset)
	}
	if req.Limit > 0 {
		limit = min(limit, int(req.Limit))
	}
	var ruleSchemas []RuleSchema
	query := s.orm
	if req.Id != "" {
		id, err := strconv.ParseUint(req.Id, 10, 64)
		if err != nil {
			return nil, status.Errorf(codes.InvalidArgument, "id: %v is invalid", req.Id)
		}
		query = query.Where("id = ?", id)
	}
	if req.Name != "" {
		query = query.Where("name = ?", req.Name)
	}
	for labelKey, labelValue := range req.Labels {
		query = query.Where("labels->>? = ?", labelKey, labelValue)
	}

	err := query.Order("id desc").Offset(offset).Limit(limit).Find(&ruleSchemas).Error
	if err != nil {
		return nil, status.Errorf(codes.Internal, "getFromDB: %v", err)
	}

	pbRuleSchemas := make([]*pb.RuleSchema, len(ruleSchemas))
	for index, ruleSchema := range ruleSchemas {
		pbRuleSchemas[index] = ruleSchema.toPb()
	}

	return &pb.GetRuleSchemasResponse{Schemas: pbRuleSchemas}, nil
}

func (s *service) UpdateRuleSchema(
	ctx context.Context, req *pb.UpdateRuleSchemaRequest,
) (*pb.UpdateRuleSchemaResponse, error) {
	var user string
	if md, ok := metadata.FromIncomingContext(ctx); ok {
		if users := md.Get("user"); len(users) > 0 {
			user = users[0]
		}
	}
	if req.Id == "" {
		return nil, status.Error(codes.InvalidArgument, "`id` is a required field")
	}

	id, err := strconv.ParseUint(req.Id, 10, 64)
	if err != nil {
		return nil, status.Errorf(codes.InvalidArgument, "id: %v is invalid", req.Id)
	}

	if req.ItemJsonSchema != "" {
		s.logger.Info().Msgf("validating the json schema: %s", req.Name)
		err := s.schemaComplier.AddResource(req.Id, strings.NewReader(req.ItemJsonSchema))
		if err != nil {
			return nil, status.Errorf(codes.Internal, "unable to add `item_json_schema` : %v", err)
		}
		_, err = s.schemaComplier.Compile(req.Id)
		if err != nil {
			return nil, status.Errorf(codes.InvalidArgument, "json schema is invalid: %v", err)
		}
	}

	err = s.orm.Updates(&RuleSchema{
		Id:             id,
		Name:           req.Name,
		Labels:         req.Labels,
		Status:         int32(req.Status),
		ItemJsonSchema: req.ItemJsonSchema,
		UpdatedBy:      user,
		Description:    req.Description,
	}).Error
	if err != nil {
		return nil, status.Errorf(codes.Internal, "updateInDB: %v", err)
	}

	return &pb.UpdateRuleSchemaResponse{}, nil
}

func (s *service) CreateRule(
	ctx context.Context, req *pb.CreateRuleRequest,
) (*pb.CreateRuleResponse, error) {
	var err error
	var schemaId uint64
	var user string
	if md, ok := metadata.FromIncomingContext(ctx); ok {
		if users := md.Get("user"); len(users) > 0 {
			user = users[0]
		}
	}
	if req.Name == "" {
		return nil, status.Error(codes.InvalidArgument, "`name` is a required field")
	}
	if req.MarketingChannel == pbenums.MarketingChannel_MARKETING_CHANNEL_UNSPECIFIED {
		return nil, status.Error(codes.InvalidArgument, "`marketing_channel` is a required field")
	}
	if req.SchemaId == "" {
		return nil, status.Error(codes.InvalidArgument, "`schema_id` is a required field")
	} else {
		schemaId, err = strconv.ParseUint(req.SchemaId, 10, 64)
		if err != nil {
			return nil, status.Errorf(
				codes.InvalidArgument, "schema)id: %v is invalid", req.SchemaId,
			)
		}
	}
	if req.AttachmentType == pb.RuleAttachmentType_RULE_ATTACHMENT_TYPE_UNSPECIFIED {
		return nil, status.Error(codes.InvalidArgument, "`attachment_type` is a required field")
	}

	r := &Rule{
		Name:             req.Name,
		MarketingChannel: int32(req.MarketingChannel),
		RuleSchemaId:     schemaId,
		AttachmentType:   int32(req.AttachmentType),
		Status:           int32(pb.RuleStatus_RULE_STATUS_ENABLED),
		Labels:           req.Labels,
		CreatedBy:        user,
	}
	err = s.orm.Create(r).Error
	if err != nil {
		return nil, status.Errorf(codes.Internal, "createInDB: %v", err)
	}

	return &pb.CreateRuleResponse{Id: strconv.FormatUint(r.Id, 10)}, nil
}

func (s *service) GetRules(
	_ context.Context, req *pb.GetRulesRequest,
) (*pb.GetRulesResponse, error) {
	if req.MarketingChannel == pbenums.MarketingChannel_MARKETING_CHANNEL_UNSPECIFIED {
		return nil, status.Error(codes.InvalidArgument, "`marketing_channel` is a required field")
	}

	var rules []Rule
	limit, offset := 100, 0
	if req.Offset > 0 {
		offset = int(req.Offset)
	}
	if req.Limit > 0 {
		limit = min(limit, int(req.Limit))
	}
	query := s.orm.Where("marketing_channel = ?", int32(req.MarketingChannel))
	if req.Name != "" {
		query = query.Where("name = ?", req.Name)
	}
	if req.Id != "" {
		id, err := strconv.ParseUint(req.Id, 10, 64)
		if err != nil {
			return nil, status.Errorf(codes.InvalidArgument, "id: %v is invalid", req.Id)
		}
		query = query.Where("id = ?", id)

	}
	if req.SchemaId != "" {
		schemaId, err := strconv.ParseUint(req.SchemaId, 10, 64)
		if err != nil {
			return nil, status.Errorf(codes.InvalidArgument, "schema_id: %v is invalid", req.Id)
		}
		query = query.Where("rule_schema_id = ?", schemaId)
	}
	if req.AttachmentType != pb.RuleAttachmentType_RULE_ATTACHMENT_TYPE_UNSPECIFIED {
		query = query.Where("attachment_type = ?", int32(req.AttachmentType))
	}
	for labelKey, labelValue := range req.Labels {
		query = query.Where("labels->>? = ?", labelKey, labelValue)
	}

	err := query.Order("id desc").Offset(offset).Limit(limit).Find(&rules).Error
	if err != nil {
		return nil, status.Errorf(codes.Internal, "getFromDB: %v", err)
	}

	pbRules := make([]*pb.Rule, len(rules))
	for index, r := range rules {
		pbRules[index] = r.toPb()
	}

	return &pb.GetRulesResponse{Rules: pbRules}, nil
}

func (s *service) UpdateRule(
	ctx context.Context, req *pb.UpdateRuleRequest,
) (*pb.UpdateRuleResponse, error) {
	var user string
	if md, ok := metadata.FromIncomingContext(ctx); ok {
		if users := md.Get("user"); len(users) > 0 {
			user = users[0]
		}
	}
	if req.Id == "" {
		return nil, status.Error(codes.InvalidArgument, "`id` is a required field")
	}

	id, err := strconv.ParseUint(req.Id, 10, 64)
	if err != nil {
		return nil, status.Errorf(codes.InvalidArgument, "id: %v is invalid", req.Id)
	}

	err = s.orm.Updates(&Rule{
		Id:        id,
		Name:      req.Name,
		Status:    int32(req.Status),
		Labels:    req.Labels,
		UpdatedBy: user,
	}).Error
	if err != nil {
		return nil, status.Errorf(codes.Internal, "updateInDB: %v", err)
	}

	return &pb.UpdateRuleResponse{}, nil
}

func (s *service) CreateRuleItem(
	ctx context.Context, req *pb.CreateRuleItemRequest,
) (*pb.CreateRuleItemResponse, error) {
	var user string
	if md, ok := metadata.FromIncomingContext(ctx); ok {
		if users := md.Get("user"); len(users) > 0 {
			user = users[0]
		}
	}
	var err error
	var ruleId uint64
	if req.RuleId == "" {
		return nil, status.Error(codes.InvalidArgument, "`rule_id` is a required field")
	} else {
		ruleId, err = strconv.ParseUint(req.RuleId, 10, 64)
		if err != nil {
			return nil, status.Errorf(codes.InvalidArgument, "`rule_id`: %v is invalid", req.RuleId)
		}
	}
	if req.Info == "" {
		return nil, status.Error(codes.InvalidArgument, "`info` is a required field")
	} else {
		err = s.validateRuleItemInfo(req.Info, ruleId)
		if err != nil {
			return nil, err
		}
	}

	r := &RuleItem{
		RuleId:       ruleId,
		Info:         req.Info,
		BusinessCase: req.BusinessCase,
		Status:       int32(pb.RuleItemStatus_RULE_ITEM_STATUS_ENABLED),
		CreatedBy:    user,
		Version:      1,
	}
	err = s.orm.Create(r).Error
	if err != nil {
		return nil, status.Errorf(codes.Internal, "createInDB: %v", err)
	}
	s.logger.Info().Msgf("rule item created: %v", r)

	return &pb.CreateRuleItemResponse{Id: strconv.FormatUint(r.Id, 10), Version: strconv.FormatUint(r.Version, 10)}, nil
}

func (s *service) GetRuleItems(
	_ context.Context, req *pb.GetRuleItemsRequest,
) (*pb.GetRuleItemsResponse, error) {
	var err error
	var ruleId uint64

	limit, offset := 100, 0
	if req.Offset > 0 {
		offset = int(req.Offset)
	}
	if req.Limit > 0 {
		limit = min(limit, int(req.Limit))
	}
	if req.RuleId == "" {
		return nil, status.Error(codes.InvalidArgument, "`rule_id` is a required field")
	} else {
		ruleId, err = strconv.ParseUint(req.RuleId, 10, 64)
		if err != nil {
			return nil, status.Errorf(codes.InvalidArgument, "rule_id: %v is invalid", req.RuleId)
		}
	}

	var ruleItems []RuleItem
	query := s.orm.Where("rule_id = ?", ruleId)
	if req.Id != "" {
		itemId, err := strconv.ParseUint(req.Id, 10, 64)
		if err != nil {
			return nil, status.Errorf(codes.InvalidArgument, "id: %v is invalid", req.Id)
		}
		query = query.Where("id = ?", itemId)
	}
	if req.Version != "" {
		version, err := strconv.ParseUint(req.Version, 10, 64)
		if err != nil {
			return nil, status.Errorf(codes.InvalidArgument, "version: %v is invalid", req.Version)
		}
		query = query.Where("version = ?", version)
	}
	if req.IncludeAllVersions {
		if req.Id == "" {
			return nil, status.Errorf(
				codes.InvalidArgument,
				"`id` is required when `include_all_versions` field is set to true",
			)
		}
		query = query.Where("status IN ?", []int32{
			int32(pb.RuleItemStatus_RULE_ITEM_STATUS_ENABLED),
			int32(pb.RuleItemStatus_RULE_ITEM_STATUS_DISABLED),
		})
	} else {
		// Fetch only the latest version for each RuleId, regardless of status
		subQuery := s.orm.Table("loki.rule_items").
			Select("id, MAX(version) AS max_version").
			Where("rule_id = ?", ruleId).
			Group("id")

		query = query.Joins("INNER JOIN (?) latest ON rule_items.id = latest.id AND rule_items.version = latest.max_version", subQuery)
	}

	err = query.Order("id desc, version desc").Offset(offset).Limit(limit).Find(&ruleItems).Error
	if err != nil {
		return nil, status.Errorf(codes.Internal, "getFromDB: %v", err)
	}

	pbRuleItems := make([]*pb.RuleItem, len(ruleItems))
	for index, ruleItem := range ruleItems {
		pbRuleItems[index] = ruleItem.toPb()
	}

	return &pb.GetRuleItemsResponse{Items: pbRuleItems}, nil
}

func (s *service) UpdateRuleItem(
	ctx context.Context, req *pb.UpdateRuleItemRequest,
) (*pb.UpdateRuleItemResponse, error) {
	var err error
	var itemId, version uint64
	var user string
	if md, ok := metadata.FromIncomingContext(ctx); ok {
		if users := md.Get("user"); len(users) > 0 {
			user = users[0]
		}
	}
	if req.Id == "" {
		return nil, status.Error(codes.InvalidArgument, "`id` is a required field")
	} else {
		itemId, err = strconv.ParseUint(req.Id, 10, 64)
		if err != nil {
			return nil, status.Errorf(codes.InvalidArgument, "id: %v is invalid", req.Id)
		}
	}
	if req.Version == "" {
		return nil, status.Error(codes.InvalidArgument, "`version` is a required field")
	} else {
		version, err = strconv.ParseUint(req.Version, 10, 64)
		if err != nil {
			return nil, status.Errorf(codes.InvalidArgument, "version: %v is invalid", req.Id)
		}
	}

	// For the case where an Item is to be disabled.
	if req.Status == pb.RuleItemStatus_RULE_ITEM_STATUS_DISABLED {
		err = s.orm.Updates(&RuleItem{
			Id:        itemId,
			Version:   version,
			Status:    int32(pb.RuleItemStatus_RULE_ITEM_STATUS_DISABLED),
			UpdatedBy: user,
		}).Error
		if err != nil {
			return nil, status.Errorf(codes.Internal, "disableRuleItemInDB: %v", err)
		}
		return &pb.UpdateRuleItemResponse{}, nil
	}

	// For the case where a new version is to be created.
	if req.Info == "" {
		return nil, status.Error(codes.InvalidArgument, "`info` is a required field")
	} else {
		var dbRuleItem RuleItem
		err = s.orm.First(&dbRuleItem, RuleItem{Id: itemId}).Error
		if err != nil {
			return nil, status.Errorf(codes.NotFound, "ruleItem: %v not found", req.Id)
		}

		err = s.validateRuleItemInfo(req.Info, dbRuleItem.RuleId)
		if err != nil {
			return nil, err
		}
	}
	var latestRuleItem RuleItem
	var ruleItem *RuleItem
	isValidInfo := true
	dbErr := s.orm.Transaction(func(tx *gorm.DB) error {
		err = tx.Clauses(clause.Locking{Strength: "UPDATE"}).Order("version desc").
			First(&latestRuleItem, "id = ?", itemId).Error
		if err != nil {
			return fmt.Errorf("selectRuleItemForUpdate: %w", err)
		}

		if latestRuleItem.Version != version {
			return nil
		}

		ruleItem = &RuleItem{
			Id:             itemId,
			RuleId:         latestRuleItem.RuleId,
			Info:           req.Info,
			VersionBasedOn: version,
			BusinessCase:   req.BusinessCase,
			Status:         int32(pb.RuleItemStatus_RULE_ITEM_STATUS_ENABLED),
			CreatedBy:      user,
			Version:        latestRuleItem.Version + 1,
		}
		err = tx.Create(ruleItem).Error
		if err != nil {
			return fmt.Errorf("createNewVersionInDB: %w", err)
		}

		latestRuleItem.Status = int32(pb.RuleItemStatus_RULE_ITEM_STATUS_DISABLED)
		err = tx.Updates(latestRuleItem).Error
		if err != nil {
			return fmt.Errorf("markPreviousVersionAsDisabledInDB: %w", err)
		}

		return nil
	})
	if dbErr != nil {
		return nil, status.Errorf(codes.Internal, "updateRuleItemInDB: %v", dbErr)
	} else if !isValidInfo {
		return nil, status.Error(codes.FailedPrecondition, "`info` does not conform to the schema")
	} else if latestRuleItem.Version != version {
		return nil, status.Errorf(
			codes.FailedPrecondition,
			"request is based on version: %v. while the latest version is: %v",
			req.Version, latestRuleItem.Version,
		)
	}
	return &pb.UpdateRuleItemResponse{NewVersion: strconv.FormatUint(ruleItem.Version, 10)}, nil
}

func (s *service) validateRuleItemInfo(ruleInfo string, ruleId uint64) error {
	var info interface{}
	err := json.Unmarshal([]byte(ruleInfo), &info)
	if err != nil {
		return status.Errorf(codes.InvalidArgument, "`info`: %v is invalid", ruleInfo)
	}

	var rule Rule
	err = s.orm.First(&rule, Rule{Id: ruleId}).Error
	if err != nil {
		return status.Errorf(codes.Internal, "getRule: %d, err: %v", ruleId, err)
	}
	var schema RuleSchema
	err = s.orm.First(&schema, RuleSchema{Id: rule.RuleSchemaId}).Error
	if err != nil {
		return status.Errorf(codes.Internal, "getSchema: %d, err: %d", rule.RuleSchemaId, err)
	}
	err = s.schemaComplier.AddResource(schema.Name, strings.NewReader(schema.ItemJsonSchema))
	if err != nil {
		return status.Errorf(codes.Internal, "unable to add `item_json_schema` : %v", err)
	}
	sch, err := s.schemaComplier.Compile(schema.Name)
	if err != nil {
		return status.Errorf(codes.Internal, "unable to compile `item_json_schema` : %v", err)
	}
	err = sch.Validate(info)
	if err != nil {
		return status.Errorf(codes.Internal, "rule item doesn't follow the json schema : %v", err)
	}
	return nil
}
