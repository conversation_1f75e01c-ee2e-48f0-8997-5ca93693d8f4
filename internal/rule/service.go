package rule

import (
	"fmt"

	env "github.com/Netflix/go-env"
	"github.com/rs/zerolog"
	"github.com/santhosh-tekuri/jsonschema/v5"
	pb "github.expedia.biz/gmo-performance-marketing/pb-go/pkg/loki/v1"
	"google.golang.org/grpc"
	"gorm.io/gorm"
)

func RegisterService(s grpc.ServiceRegistrar, orm *gorm.DB, logger zerolog.Logger) error {
	svc, err := newService(orm, logger)
	if err != nil {
		return err
	}

	pb.RegisterRuleServiceServer(s, svc)
	logger.Info().Msg("registered rule service")
	return nil
}

type serviceConfig struct{}

type service struct {
	pb.UnimplementedRuleServiceServer

	cfg            serviceConfig
	orm            *gorm.DB
	logger         zerolog.Logger
	schemaComplier *jsonschema.Compiler
}

func newService(orm *gorm.DB, logger zerolog.Logger) (*service, error) {
	var svcCfg serviceConfig
	_, err := env.UnmarshalFromEnviron(&svcCfg)
	if err != nil {
		return nil, fmt.Errorf("unmarshalConfigFromEnv: %w", err)
	}

	err = orm.AutoMigrate(&Rule{}, &RuleSchema{}, &RuleItem{})
	if err != nil {
		return nil, fmt.Errorf("autoMigrate: %w", err)
	}

	return &service{
		cfg:            svcCfg,
		orm:            orm,
		logger:         logger.With().Str("service", "loki.rule").Logger(),
		schemaComplier: jsonschema.NewCompiler(),
	}, nil
}
