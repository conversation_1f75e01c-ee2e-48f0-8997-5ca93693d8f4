package main

import (
	"github.com/Netflix/go-env"
	"github.expedia.biz/gmo-performance-marketing/loki/internal/client"
	loggerlib "github.expedia.biz/gmo-performance-marketing/sem-lib-go/pkg/logger"
)

type pollerConfig struct {
	LokiSvcAddr string `env:"LOKI_SERVICE_ADDR,default:loki-server.marketing.expedia.com:443"`
}

func main() {
	var config pollerConfig

	logger := loggerlib.Get().With().Str("workload", "loki.pipelinerunpoller").Logger()

	logger.Info().Msg("starting pipeline run poller ....")
	_, err := env.UnmarshalFromEnviron(&config)
	if err != nil {
		logger.Fatal().Err(err).Msg("failed to init pipelinerunpoller config")
	}
	logger.Info().Msgf("config: %+v", config)

	pipelineClient, err := client.GetNewPipelineClient(logger, config.LokiSvcAddr)
	if err != nil {
		logger.Fatal().Err(err).Msg("failed to init pipeline client")
	}

	logger.Info().Msgf("pipelineClient intialized")

	err = pipelineClient.RunControlLoop()
	if err != nil {
		logger.Fatal().Err(err).Msg("failed to run control loop")
	}
	logger.Info().Msg("finished pipeline run poller ....")
}
