package main

import (
	"flag"
	"fmt"

	"github.com/rs/zerolog"
	"github.expedia.biz/gmo-performance-marketing/loki/internal/assembler/config"
	pb "github.expedia.biz/gmo-performance-marketing/pb-go/pkg/loki/v1"

	"github.expedia.biz/gmo-performance-marketing/loki/internal/assembler/entity"
	"github.expedia.biz/gmo-performance-marketing/sem-lib-go/pkg/logger"
)

func main() {
	l := logger.Get().With().Str("service", "assembler").Logger()

	runId := flag.String("runId", "", "unique identifier of assembler run")
	workflowId := flag.String("workflowId", "", "argo workflow name")
	flag.Parse()

	fatalForNilOrEmptyStringFlag("runId", runId, l)
	fatalForNilOrEmptyStringFlag("workflowId", workflowId, l)
	// get assembler config
	cfg, err := config.GetEntityConfig()
	if err != nil {
		l.Fatal().Err(err).Msg("failed to load assembler config")
	}
	l.Info().Msgf("configuration loaded: %v", cfg)

	job := entity.NewJob(l, cfg, *runId, *workflowId)
	err = job.Init()
	if err != nil {
		l.Fatal().Err(err).Msg("failed to initialize job")
	}

	assemblerRun, err := job.AssemblerClient.GetAssemblerRun(*runId)
	if err != nil {
		l.Fatal().Err(err).Msg("failed to load assembler run")
	}

	orchestratorContext := assemblerRun.OrchestratorExecContext
	// This is case for when there is no bid assembly step and entity generation is the 1st step
	if assemblerRun.OrchestratorExecContext == nil {
		orchestratorContext := &pb.OrchestratorExecContext{}
		// Create tracking URL
		orchestratorContext.TrackingUrl = fmt.Sprintf("%s/%s", cfg.TrackingUrl, *workflowId)
		orchestratorContext.ExecutionStatus = pb.OrchestratorExecutionStatus_ORCHESTRATOR_EXECUTION_STATUS_RUNNING
		assemblerRun.OrchestratorExecContext = orchestratorContext
		// Update assembler run tracking
		if err := job.AssemblerClient.UpdateAssemblerRunTracking(*runId, orchestratorContext); err != nil {
			l.Fatal().Err(err).Msg("failed to update assembler run")
		}
	}

	err = job.Run(assemblerRun)
	if err != nil {
		l.Error().Err(err).Msg("failed to run entity")
		orchestratorContext.IsError = true
		orchestratorContext.ErrorMessage = err.Error()
	}
	err = job.AssemblerClient.UpdateAssemblerRunTracking(*runId, orchestratorContext)
	if err != nil {
		l.Fatal().Err(err).Msg("failed to update assembler run")
	}
}

func fatalForNilOrEmptyStringFlag(name string, value *string, logger zerolog.Logger) {
	if value == nil || *value == "" {
		logger.Fatal().Msgf("'%s' can't be empty", name)
	}
}
