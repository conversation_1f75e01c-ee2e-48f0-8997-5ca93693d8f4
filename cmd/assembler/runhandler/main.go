package main

import (
	"flag"
	"fmt"

	"github.com/rs/zerolog"
	"github.expedia.biz/gmo-performance-marketing/loki/internal/assembler/config"
	"github.expedia.biz/gmo-performance-marketing/loki/internal/client"
	pb "github.expedia.biz/gmo-performance-marketing/pb-go/pkg/loki/v1"
	"github.expedia.biz/gmo-performance-marketing/sem-lib-go/pkg/logger"
)

func main() {
	l := logger.Get().With().Str("service", "assembler.runhandler").Logger()

	runId := flag.String("runId", "", "unique identifier of assembler run")
	status := flag.String("status", "", "status")
	flag.Parse()

	fatalForNilOrEmptyStringFlag("runId", runId, l)
	fatalForNilOrEmptyStringFlag("status", status, l)

	cfg, err := config.GetRunHandlerConfig()
	if err != nil {
		l.Fatal().Err(err).Msg("failed to load run handler config")
	}
	l.Info().Msgf("configuration loaded: %v", cfg)

	client, err := client.NewAssemblerClient(cfg.LokiSvcAddr, l)
	if err != nil {
		l.Fatal().Err(err).Msg("failed to create client")
	}

	run, err := client.GetAssemblerRun(*runId)

	orchestratorContext := run.OrchestratorExecContext
	orchestratorContext.ExecutionStatus = pb.OrchestratorExecutionStatus_ORCHESTRATOR_EXECUTION_STATUS_FINISHED

	if *status != "Succeeded" {
		orchestratorContext.IsError = true
		orchestratorContext.ErrorMessage = fmt.Sprintf("Run exited with error code: Failed. check run logs")
	}

	err = client.UpdateAssemblerRunTracking(*runId, orchestratorContext)
	if err != nil {
		l.Fatal().Err(err).Msg("failed to update run tracking status")
	}
}

func fatalForNilOrEmptyStringFlag(name string, value *string, logger zerolog.Logger) {
	if value == nil || *value == "" {
		logger.Fatal().Msgf("'%s' can't be empty", name)
	}
}
