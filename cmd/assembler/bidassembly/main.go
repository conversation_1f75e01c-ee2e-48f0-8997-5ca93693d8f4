package main

import (
	"flag"
	"fmt"

	"github.com/rs/zerolog"
	"github.expedia.biz/gmo-performance-marketing/loki/internal/assembler/bidassembly"
	"github.expedia.biz/gmo-performance-marketing/loki/internal/assembler/config"
	pb "github.expedia.biz/gmo-performance-marketing/pb-go/pkg/loki/v1"
	"github.expedia.biz/gmo-performance-marketing/sem-lib-go/pkg/logger"
)

func main() {
	l := logger.Get().With().Str("service", "assembler").Logger()

	runId := flag.String("runId", "", "unique identifier of assembler run")
	workflowId := flag.String("workflowId", "", "argo workflow name")
	overrideRunParameters := flag.String("overrideRunParameters", "", "override run parameters")
	flag.Parse()

	fatalForNilOrEmptyStringFlag("runId", runId, l)
	fatalForNilOrEmptyStringFlag("workflowId", workflowId, l)
	// get assembler config
	l = l.With().Str("runId", *runId).Logger()
	cfg, err := config.GetBidAssemblyConfig()
	if err != nil {
		l.Fatal().Err(err).Msg("failed to load bidassembly config")
	}
	l.Info().Msgf("configuration loaded: %v", cfg)

	job, err := bidassembly.NewJob(l, cfg, *runId, *workflowId, overrideRunParameters)
	if err != nil {
		l.Fatal().Err(err).Msg("failed to create job")
	}
	err = job.Init()
	if err != nil {
		l.Fatal().Err(err).Msg("failed to initialize job")
	}

	orchestratorContext := &pb.OrchestratorExecContext{}
	// Create tracking URL
	orchestratorContext.TrackingUrl = fmt.Sprintf("%s/%s", cfg.TrackingUrl, *workflowId)
	orchestratorContext.ExecutionStatus = pb.OrchestratorExecutionStatus_ORCHESTRATOR_EXECUTION_STATUS_RUNNING

	// Update assembler run tracking
	err = job.AssemblerClient.UpdateAssemblerRunTracking(*runId, orchestratorContext)
	if err != nil {
		l.Fatal().Err(err).Msg("failed to update tracking run")
	}

	err = job.Run()
	if err != nil {
		l.Error().Err(err).Msg("failed to run job")
		orchestratorContext.IsError = true
		orchestratorContext.ErrorMessage = err.Error()
		trackingErr := job.AssemblerClient.UpdateAssemblerRunTracking(*runId, orchestratorContext)
		if trackingErr != nil {
			l.Fatal().Err(err).Msg("failed to update tracking run")
		}
		l.Fatal().Err(err).Msgf("failed to run job")
	}
}

func fatalForNilOrEmptyStringFlag(name string, value *string, logger zerolog.Logger) {
	if value == nil || *value == "" {
		logger.Fatal().Msgf("'%s' can't be empty", name)
	}
}
