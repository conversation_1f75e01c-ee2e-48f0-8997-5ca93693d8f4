package job

import (
	"fmt"
	"reflect"

	"github.com/Netflix/go-env"
)

type config struct {
	Partner               string `env:"PARTNER,default=gha"`
	LokiSvcAddr           string `env:"LOKI_SERVICE_ADDR,default=localhost:50051"`
	AwsRegion             string `env:"AWS_REGION,default=us-west-2"`
	Env                   string `env:"ENV,default=DEV"`
	StructureFilesS3Path  string `env:"STRUCTURE_FILES_S3_PATH,default=s3://eg-marketing-platform-test/structure"`
	BidUnitS3Path         string `env:"BID_UNITS_S3_PATH,default=s3://eg-marketing-platform-test/loki/bid-units"`
	WorkspaceDir          string `env:"WORKSPACE_DIR,default=./workspace"`
	StructureQuerySvcAddr string `env:"STRUCTURE_QUERY_SERVICE_ADDR,default=structure-query-svc.test.marketing.expedia.com:443"`
	OdinSvcAddr           string `env:"ODIN_SERVICE_ADDR,default=sem-misc-go.test.marketing.expedia.com:443"`
	SlackChannel          string `env:"SLACK_CHANNEL,default=#eg-mtt-loki-alerts"`
	TrackingUrl           string `env:"TRACKING_URL,default=https://argo-workflows.test.marketing.expedia.com/workflows/loki"`
	MaxWorkers            int    `env:"MAX_WORKERS,default=5"`
	RequestTimeout        int    `env:"REQUEST_TIMEOUT,default=30"`
}

var partnerRequiredFields = map[string][]string{
	"gha": {
		"LokiSvcAddr", "AwsRegion", "StructureFilesS3Path", "BidUnitS3Path", "WorkspaceDir", "StructureQuerySvcAddr",
		"OdinSvcAddr", "TrackingUrl", "MaxWorkers", "RequestTimeout",
	},
	"trivago": {
		"LokiSvcAddr", "AwsRegion", "BidUnitS3Path", "WorkspaceDir", "TrackingUrl", "MaxWorkers", "RequestTimeout",
	},
}

func (c *config) validate() error {
	if c.MaxWorkers <= 0 {
		c.MaxWorkers = 5
	}
	if c.RequestTimeout <= 0 {
		c.RequestTimeout = 30
	}

	partner := c.Partner
	requiredFields, ok := partnerRequiredFields[partner]
	if !ok {
		return fmt.Errorf("unknown partner: %s", partner)
	}

	val := reflect.ValueOf(*c)
	typ := reflect.TypeOf(*c)
	for _, field := range requiredFields {
		f, found := typ.FieldByName(field)
		if !found {
			return fmt.Errorf("config field %s not found for partner %s", field, partner)
		}
		v := val.FieldByName(field)
		if v.Kind() == reflect.String && v.String() == "" {
			return fmt.Errorf("%s is required for partner %s", f.Name, partner)
		}
	}
	return nil
}

func GetConfig() (*config, error) {
	var cfg config

	_, err := env.UnmarshalFromEnviron(&cfg)
	if err != nil {
		return nil, fmt.Errorf("failed to unmarshal bid unit job config: %w", err)
	}

	if err := cfg.validate(); err != nil {
		return nil, fmt.Errorf("invalid configuration: %w", err)
	}

	return &cfg, nil
}
