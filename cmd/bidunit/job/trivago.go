package job

import (
	"bufio"
	"encoding/json"
	"fmt"
	"github.com/rs/zerolog"
	"github.com/xitongsys/parquet-go/parquet"
	"github.com/xitongsys/parquet-go/writer"
	"github.expedia.biz/gmo-performance-marketing/loki/internal/client"
	pb "github.expedia.biz/gmo-performance-marketing/pb-go/pkg/loki/v1"
	"github.expedia.biz/gmo-performance-marketing/sem-lib-go/pkg/aws"
	"github.expedia.biz/gmo-performance-marketing/sem-lib-go/pkg/enums"
	"os"
	"path/filepath"
	"strings"
)

type InputBidUnit struct {
	// Zerolog (and most Go loggers using reflection) only serializes exported fields (those starting with uppercase letters)
	Name             string
	MarketingChannel string
	Labels           map[string]string
	Status           string
}

type tvg struct {
	awsClient     aws.Client
	bidUnitClient client.BidUnitClient

	cfg    *config
	logger zerolog.Logger
}

type Grain struct {
	Brand       string `parquet:"Name=brand, type=BYTE_ARRAY, encoding=PLAIN_DICTIONARY"`
	Lob         string `parquet:"Name=lob, type=BYTE_ARRAY, encoding=PLAIN_DICTIONARY"`
	Posa        string `parquet:"Name=posa, type=BYTE_ARRAY, encoding=PLAIN_DICTIONARY"`
	AuctionType string `parquet:"Name=auction_type, type=BYTE_ARRAY, encoding=PLAIN_DICTIONARY"`
	Status      string `parquet:"Name=status, type=BYTE_ARRAY"`
}

func getTrivagoBidUnitJob(cfg *config, logger zerolog.Logger, awsClient aws.Client, bidUnitClient client.BidUnitClient) (*tvg, error) {

	return &tvg{
		cfg:           cfg,
		logger:        logger,
		awsClient:     awsClient,
		bidUnitClient: bidUnitClient,
	}, nil
}

func (g *tvg) GenerateBidUnits(inChan chan<- LokiBidUnit) error {
	bidUnitsFromFiles, err := loadBidUnitsFromLocalResourceDir("./resources/trivago/bid_units/", g.logger)
	if err != nil {
		return fmt.Errorf("failed to load trivago bid units from local resource files: %w", err)
	}

	err = validateInputBidUnitObjects(bidUnitsFromFiles)
	if err != nil {
		return err
	}

	inputBidUnits := transformToInputBidUnits(bidUnitsFromFiles, g.logger)
	bidUnitsFromDB, err := g.bidUnitClient.GetBidUnitsByChannelAndLabels(enums.GetMarketingChannelFromString(g.MarketingChannel()), nil)
	if err != nil {
		return fmt.Errorf("failed to fetch existing bid units from Loki: %w", err)
	}

	// Build dbLookup map
	dbLookup := make(map[string]*pb.BidUnit)
	for _, dbBidUnit := range bidUnitsFromDB {
		key := dbBidUnit.Labels["channel"] + "|" +
			dbBidUnit.Labels["lob"] + "|" +
			dbBidUnit.Labels["brand"] + "|" +
			dbBidUnit.Labels["partner"] + "|" +
			dbBidUnit.Labels["posa"] + "|" +
			dbBidUnit.Labels["auction"]
		dbLookup[key] = dbBidUnit
	}

	outputPath := fmt.Sprintf("%s/outputs/file.parquet", g.cfg.WorkspaceDir)
	w, err := os.Create(outputPath)
	if err != nil {
		return fmt.Errorf("failed to create output file: %w", err)
	}
	defer w.Close()
	pw, err := writer.NewParquetWriterFromWriter(w, new(Grain), 2)
	if err != nil {
		return fmt.Errorf("failed to create parquet writer: %w", err)
	}
	pw.RowGroupSize = 128 * 1024 * 1024
	pw.CompressionType = parquet.CompressionCodec_SNAPPY

	// Process inputBidUnits sequentially
	for _, inputBidUnit := range inputBidUnits {
		key := inputBidUnit.Labels["channel"] + "|" +
			inputBidUnit.Labels["lob"] + "|" +
			inputBidUnit.Labels["brand"] + "|" +
			inputBidUnit.Labels["partner"] + "|" +
			inputBidUnit.Labels["posa"] + "|" +
			inputBidUnit.Labels["auction"]

		var id string
		if dbBidUnit, found := dbLookup[key]; found {
			delete(dbLookup, key)
			if getLokiBidUnitStatusFromString(inputBidUnit.Status) == getLokiBidUnitStatusFromString(dbBidUnit.Status) {
				continue
			}
			id = dbBidUnit.Id
		}

		labels := inputBidUnit.Labels
		grain := Grain{
			Brand:       labels["brand"],
			Lob:         labels["lob"],
			Posa:        labels["posa"],
			AuctionType: labels["auction"],
			Status:      inputBidUnit.Status,
		}

		if err := pw.Write(grain); err != nil {
			g.logger.Warn().Err(err).
				Interface("grain", grain).
				Msg("failed to write Trivago bid unit to parquet file")
		}

		status := getLokiBidUnitStatusFromString(inputBidUnit.Status)
		lokiBidUnit := LokiBidUnit{
			Id:               id,
			Name:             inputBidUnit.Name,
			MarketingChannel: inputBidUnit.MarketingChannel,
			Labels:           labels,
			Status:           status,
		}
		g.logger.Info().Interface("lokiBidUnit", lokiBidUnit).Msg("Processing bid unit for Loki")
		inChan <- lokiBidUnit
	}

	// Disable remaining bid units in DB not in input files, sequentially
	for _, dbBidUnit := range dbLookup {
		labels := make(map[string]string)
		for k, v := range dbBidUnit.Labels {
			labels[k] = v
		}
		grain := Grain{
			Brand:       labels["brand"],
			Lob:         labels["lob"],
			Posa:        labels["posa"],
			AuctionType: labels["auction"],
			Status:      DISABLED.String(),
		}
		if err := pw.Write(grain); err != nil {
			g.logger.Warn().Err(err).
				Interface("grain", grain).
				Msg("failed to write Trivago bid unit to parquet file")
		}
		lokiBidUnit := LokiBidUnit{
			Id:               dbBidUnit.Id,
			Name:             dbBidUnit.Name,
			MarketingChannel: g.MarketingChannel(),
			Labels:           labels,
			Status:           DISABLED,
		}
		g.logger.Info().Interface("lokiBidUnit", lokiBidUnit).Msg("Disabling existing bid unit in Loki")
		inChan <- lokiBidUnit
	}

	if err := pw.WriteStop(); err != nil {
		return fmt.Errorf("failed to finalize parquet file: %w", err)
	}

	g.logger.Info().Msg("successfully generated bid units")
	return nil
}

func (g *tvg) MarketingChannel() string {
	return "trivago"
}

func loadBidUnitsFromLocalResourceDir(dir string, logger zerolog.Logger) ([]map[string]interface{}, error) {
	files, err := os.ReadDir(dir)
	if err != nil {
		return nil, err
	}

	var allObjects []map[string]interface{}
	for _, file := range files {
		if file.IsDir() || filepath.Ext(file.Name()) != ".jsonl" {
			logger.Warn().Str("file", file.Name()).Msg("Skipping non-JSONL file")
			continue
		}
		f, err := os.Open(filepath.Join(dir, file.Name()))
		if err != nil {
			return nil, err
		}
		defer f.Close()

		scanner := bufio.NewScanner(f)
		for scanner.Scan() {
			line := scanner.Text()
			if strings.TrimSpace(line) == "" {
				continue
			}
			var obj map[string]interface{}
			if err := json.Unmarshal([]byte(line), &obj); err != nil {
				return nil, fmt.Errorf("file %s has invalid JSONL line: %w", file.Name(), err)
			}
			allObjects = append(allObjects, obj)
		}
		if err := scanner.Err(); err != nil {
			return nil, err
		}
	}
	return allObjects, nil
}

func getLokiBidUnitStatusFromString(val interface{}) LokiBidUnitStatus {
	str, ok := val.(string)
	if !ok {
		return UNSPECIFIED
	}
	switch str {
	case "BID_UNIT_STATUS_ENABLED":
		return ENABLED
	case "BID_UNIT_STATUS_DISABLED":
		return DISABLED
	default:
		return UNSPECIFIED
	}
}

func transformToInputBidUnits(bidUnitsFromFiles []map[string]interface{}, logger zerolog.Logger) []InputBidUnit {
	var inputBidUnits []InputBidUnit
	for _, bu := range bidUnitsFromFiles {
		labelsInterface := bu["labels"]
		labelsDict := convertLabelsToStringMap(labelsInterface)
		name := strings.TrimSpace(bu["name"].(string))
		marketingChannel := strings.TrimSpace(bu["marketingChannel"].(string))
		status := strings.TrimSpace(bu["status"].(string))
		inputBidUnits = append(inputBidUnits, InputBidUnit{
			Name:             name,
			MarketingChannel: marketingChannel,
			Labels:           labelsDict,
			Status:           status,
		})
	}
	return inputBidUnits
}

func convertLabelsToStringMap(labelsInterface interface{}) map[string]string {
	labelsStr := make(map[string]string)
	labels := labelsInterface.(map[string]interface{})
	for k, v := range labels {
		if strVal, ok := v.(string); ok {
			labelsStr[k] = strings.TrimSpace(strVal)
		}
	}
	return labelsStr
}

func (b InputBidUnit) String() string {
	return fmt.Sprintf(
		"bidUnit[Name=%s, MarketingChannel=%s, Status=%s, Labels=%v]",
		b.Name, b.MarketingChannel, b.Status, b.Labels,
	)
}

func validateInputBidUnitObjects(bidUnitsFromFile []map[string]interface{}) error {
	requiredBidUnitKeys := []string{"name", "marketingChannel", "labels", "status"}
	requiredBidUnitLabelKeys := []string{"channel", "lob", "brand", "partner", "posa", "auction"}

	for _, obj := range bidUnitsFromFile {
		// Check top-level keys
		for _, key := range requiredBidUnitKeys {
			val, exists := obj[key]
			if !exists || val == nil {
				return fmt.Errorf("missing or nil key '%s' in bid unit object: %+v", key, obj)
			}
			// For string keys, check non-empty
			if key != "labels" {
				strVal, ok := val.(string)
				if !ok || strVal == "" {
					return fmt.Errorf("empty or invalid string for key '%s' in bid unit object: %+v", key, obj)
				}
			}
		}
		// Check labels sub-keys
		labels, ok := obj["labels"].(map[string]interface{})
		if !ok {
			return fmt.Errorf("labels field is not a map[string]interface{} in bid unit object: %+v", obj)
		}
		for _, lkey := range requiredBidUnitLabelKeys {
			lval, lexists := labels[lkey]
			strLval, ok := lval.(string)
			if !lexists || !ok || strLval == "" {
				return fmt.Errorf("missing or invalid label '%s' in bid unit object: %+v", lkey, obj)
			}
		}
	}
	return nil
}

func (s LokiBidUnitStatus) String() string {
	switch s {
	case ENABLED:
		return "BID_UNIT_STATUS_ENABLED"
	case DISABLED:
		return "BID_UNIT_STATUS_DISABLED"
	default:
		return "BID_UNIT_STATUS_UNSPECIFIED"
	}
}
