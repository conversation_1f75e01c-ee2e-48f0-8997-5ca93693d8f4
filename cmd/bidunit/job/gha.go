package job

import (
	"bufio"
	"encoding/csv"
	"fmt"
	"io"
	"os"
	"regexp"
	"strconv"
	"strings"
	"sync"

	"github.com/rs/zerolog"
	"github.com/xitongsys/parquet-go/parquet"
	"github.com/xitongsys/parquet-go/writer"
	"github.expedia.biz/gmo-performance-marketing/loki/internal/client"
	"github.expedia.biz/gmo-performance-marketing/sem-lib-go/pkg/aws"
	"github.expedia.biz/gmo-performance-marketing/sem-lib-go/pkg/enums"
)

// Precompiled regular expressions for better performance
var (
	brandRegex         = regexp.MustCompile(`^([^:]+)`)
	partnerPosRegex    = regexp.MustCompile(`^(?:[^:]*:){1}([^:]+)`)
	platformRegex      = regexp.MustCompile(`^(?:[^:]*:){4}([^:]+)`)
	abwRegex           = regexp.MustCompile(`^(?:[^:]*:){5}[^:]*:(\d+)_TO_(\d+)`)
	placementTypeRegex = regexp.MustCompile(`^(?:[^:]*:){9}([^:]+)`)
)

// Predefined partner POS lists for better performance
var (
	roaHcomPartnerPosList    = "AF,AS,BD,BN,BT,CC,CK,CN,CX,FJ,GU,FM,HM,IO,KG,KH,KI,KZ,LA,LK,MH,MM,MN,MO,MP,MV,NC,NF,NP,NR,NU,PF,PG,PK,PN,PW,SB,TF,TJ,TK,TL,TM,TO,TV,UM,VN,VU,WF,WS,ID,IN,MY,PH,TH,UZ"
	roaExpediaPartnerPosList = "AF,AS,BD,BN,BT,CC,CK,CN,CX,FJ,GU,FM,HM,IO,KG,KH,KI,KZ,LA,LK,MH,MM,MN,MO,MP,MV,NC,NF,NP,NR,NU,PF,PG,PK,PN,PW,SB,TF,TJ,TK,TL,TM,TO,TV,UM,VN,VU,WF,WS,ID,MY,PH,TH,UZ"
	roeHcomPartnerPosList    = "AD,AE,AL,AM,AO,AT,AX,AZ,BA,BE,BF,BG,BH,BI,BJ,BV,BW,BY,CD,CF,CG,CI,CM,CV,CY,CZ,DJ,DZ,EE,EG,EH,ER,ET,FO,GA,GE,GG,GH,GI,GL,GM,GN,GQ,GR,GW,HR,HU,IE,IL,IM,IQ,IR,IS,JE,JO,KE,KM,KW,LB,LI,LR,LS,LT,LU,LV,LY,MA,MC,MD,ME,MG,MK,ML,MR,MT,MU,MW,MZ,NA,NE,NG,OM,PL,PM,PS,PT,QA,RE,RO,RS,RU,RW,SA,SC,SD,SH,SI,SJ,SK,SL,SM,SN,SO,SS,ST,SY,SZ,TD,TF,TG,TN,TZ,UA,UG,VA,XK,YE,YT,ZA,ZM,ZW,ZZ"
	roeExpediaPartnerPosList = "AD,AE,AL,AM,AO,AT,AX,AZ,BA,BE,BF,BG,BH,BI,BJ,BV,BW,BY,CD,CF,CG,CI,CM,CV,CY,CZ,DJ,DZ,EE,EG,EH,ER,ET,FI,FO,GA,GE,GG,GH,GI,GL,GM,GN,GQ,GR,GW,HR,HU,IE,IL,IM,IQ,IR,IS,JE,JO,KE,KM,KW,LB,LI,LR,LS,LT,LU,LV,LY,MA,MC,MD,ME,MG,MK,ML,MR,MT,MU,MW,MZ,NA,NE,NG,OM,PL,PM,PS,PT,QA,RE,RO,RS,RU,RW,SA,SC,SD,SH,SI,SJ,SK,SL,SM,SN,SO,SS,ST,SY,SZ,TD,TF,TG,TN,TR,TZ,UA,UG,VA,XK,YE,YT,ZA,ZM,ZW,ZZ"
	rolPartnerPosList        = "AI,AQ,AR,CL,CO,CW,AG,AW,BB,BL,BM,BO,BQ,BS,BZ,CR,DM,DO,EC,FK,GD,GF,GP,GS,GT,GY,HN,HT,JM,KN,KY,LC,MF,MQ,MS,NI,PA,PE,PR,PY,SR,SV,SX,TC,TT,UY,VC,VG,VI,VE"
)

type gha struct {
	structureQueryClient structureQueryClient
	awsClient            aws.Client
	bidUnitClient        client.BidUnitClient

	cfg    *config
	logger zerolog.Logger
}

type Campaign struct {
	AccountName  string            `parquet:"name=account_name, type=BYTE_ARRAY, encoding=PLAIN_DICTIONARY"`
	AccountId    string            `parquet:"name=account_id, type=BYTE_ARRAY, encoding=PLAIN_DICTIONARY"`
	CampaignName string            `parquet:"name=campaign_name, type=BYTE_ARRAY, encoding=PLAIN_DICTIONARY"`
	CampaignId   string            `parquet:"name=campaign_id, type=BYTE_ARRAY, encoding=PLAIN_DICTIONARY"`
	Status       LokiBidUnitStatus `parquet:"name=status, type=BYTE_ARRAY"`

	CampaignPartnerPos string `parquet:"name=campaign_partner_pos, type=BYTE_ARRAY, encoding=PLAIN_DICTIONARY"`
	CampaignPlatform   string `parquet:"name=campaign_platform, type=BYTE_ARRAY, encoding=PLAIN_DICTIONARY"`
	AbwLowerRange      string `parquet:"name=abw_lower_range, type=BYTE_ARRAY, encoding=PLAIN_DICTIONARY"`
	AbwUpperRange      string `parquet:"name=abw_upper_range, type=BYTE_ARRAY, encoding=PLAIN_DICTIONARY"`
	PlacementType      string `parquet:"name=placement_type, type=BYTE_ARRAY, encoding=PLAIN_DICTIONARY"`
	BrandName          string `parquet:"name=brand_name, type=BYTE_ARRAY, encoding=PLAIN_DICTIONARY"`
	AnchorPartnerPos   string `parquet:"name=anchor_partner_pos, type=BYTE_ARRAY, encoding=PLAIN_DICTIONARY"`
	PartnerPosList     string `parquet:"name=partner_pos_list, type=BYTE_ARRAY, encoding=PLAIN_DICTIONARY"`
	PlatformList       string `parquet:"name=platform_list, type=BYTE_ARRAY, encoding=PLAIN_DICTIONARY"`
	AbwMinrangeStart   string `parquet:"name=abw_min_range_start, type=BYTE_ARRAY, encoding=PLAIN_DICTIONARY"`
	AbwMaxrangeStart   string `parquet:"name=abw_max_range_start, type=BYTE_ARRAY, encoding=PLAIN_DICTIONARY"`
	AnchorPlatform     string `parquet:"name=anchor_platform, type=BYTE_ARRAY, encoding=PLAIN_DICTIONARY"`
	SuperRegion        string `parquet:"name=super_region, type=BYTE_ARRAY, encoding=PLAIN_DICTIONARY"`
}

type multiplierValueRow struct {
	rangeStart      string
	rangeEnd        string
	multiplierValue string
}

type bidUnit struct {
	id string

	campaignId   string
	campaignName string
	status       LokiBidUnitStatus
}

func getGhaBidUnitJob(cfg *config, logger zerolog.Logger, awsClient aws.Client, bidUnitClient client.BidUnitClient) (*gha, error) {
	structureQueryClient, err := newStructureQueryClient(cfg.StructureQuerySvcAddr, logger)
	if err != nil {
		return nil, fmt.Errorf("could not create structure query client: %v", err)
	}
	return &gha{
		structureQueryClient: structureQueryClient,
		cfg:                  cfg,
		logger:               logger,
		awsClient:            awsClient,
		bidUnitClient:        bidUnitClient,
	}, nil
}

func (g *gha) GenerateBidUnits(inChan chan<- LokiBidUnit) error {
	g.logger.Info().Msg("fetching accounts from structure service")
	accountIds, err := g.getAccountIdsFromStructureSvc()
	if err != nil {
		return fmt.Errorf("could not get account ids: %v", err)
	}

	g.logger.Info().Msg("loading pos mapping file")
	posMapping, err := g.downloadAndLoadPosMapping()
	if err != nil {
		return fmt.Errorf("failed to load pos mapping: %w", err)
	}

	g.logger.Info().Msg("loading multiplier value mapping")
	multiplierValueMapping, err := g.downloadAndLoadMultiplierValueMapping()
	if err != nil {
		return fmt.Errorf("failed to load multiplier value mapping: %w", err)
	}

	// Create output file
	outputPath := fmt.Sprintf("%s/outputs/file.parquet", g.cfg.WorkspaceDir)
	w, err := os.Create(outputPath)
	if err != nil {
		return fmt.Errorf("failed to create output file: %w", err)
	}
	defer w.Close()

	// Initialize parquet writer
	pw, err := writer.NewParquetWriterFromWriter(w, new(Campaign), 4)
	if err != nil {
		return fmt.Errorf("failed to create parquet writer: %w", err)
	}
	pw.RowGroupSize = 128 * 1024 * 1024 // 128 MB
	pw.CompressionType = parquet.CompressionCodec_SNAPPY

	g.logger.Info().Msgf("processing %d accounts", len(accountIds))

	// Process accounts concurrently with a worker pool
	var wg sync.WaitGroup
	maxWorkers := 5 // Limit concurrent account processing
	semaphore := make(chan struct{}, maxWorkers)

	// Create a mutex to protect parquet writer
	var pwMutex sync.Mutex

	for _, accountId := range accountIds {
		wg.Add(1)
		semaphore <- struct{}{} // Acquire semaphore

		go func(accId string) {
			defer wg.Done()
			defer func() { <-semaphore }() // Release semaphore

			err := g.runForAccount(accId, posMapping, multiplierValueMapping, inChan, pw, &pwMutex)
			if err != nil {
				g.logger.Error().Err(err).Msgf("failed processing account: %s", accId)
			}
		}(accountId)
	}

	wg.Wait()

	// Finalize parquet file
	if err := pw.WriteStop(); err != nil {
		g.logger.Error().Err(err).Msg("failed to finalize parquet file")
		return fmt.Errorf("failed to finalize parquet file: %w", err)
	}

	g.logger.Info().Msg("successfully generated bid units")
	return nil
}

func (g *gha) MarketingChannel() string {
	return "google-hotel-ads"
}

func (g *gha) downloadAndLoadPosMapping() (map[string]string, error) {
	posMappingLocalPath := "./resources/eg_pos_mapping.csv"

	file, err := os.Open(posMappingLocalPath)
	if err != nil {
		return nil, fmt.Errorf("failed to open file: %s: %w", posMappingLocalPath, err)
	}
	defer file.Close()

	reader := csv.NewReader(file)
	headers, err := reader.Read()
	if err != nil {
		return nil, fmt.Errorf("failed to read headers: %w", err)
	}

	headerIndexMap := make(map[string]int)
	for i, field := range headers {
		headerIndexMap[field] = i
	}

	// Check for required columns
	if _, exists := headerIndexMap["pos_short_code"]; !exists {
		return nil, fmt.Errorf("required column 'pos_short_code' missing in pos mapping file")
	}
	if _, exists := headerIndexMap["pos_code"]; !exists {
		return nil, fmt.Errorf("required column 'pos_code' missing in pos mapping file")
	}

	posMapping := make(map[string]string)
	for {
		row, err := reader.Read()
		if err == io.EOF {
			break
		}
		if err != nil {
			return nil, fmt.Errorf("failed to read row: %w", err)
		}
		posMapping[row[headerIndexMap["pos_short_code"]]] = row[headerIndexMap["pos_code"]]
	}

	return posMapping, nil
}

func (g *gha) downloadAndLoadMultiplierValueMapping() ([]multiplierValueRow, error) {
	localFilePath := "./resources/google_multiplier_value_mapping.csv"

	file, err := os.Open(localFilePath)
	if err != nil {
		return nil, fmt.Errorf("failed to open multiplier value mapping file: %w", err)
	}
	defer file.Close()

	reader := csv.NewReader(bufio.NewReader(file))

	headers, err := reader.Read()
	if err != nil {
		return nil, fmt.Errorf("failed to read headers: %w", err)
	}

	headerIndexMap := make(map[string]int)
	for i, field := range headers {
		headerIndexMap[field] = i
	}

	// Check for required columns
	requiredColumns := []string{"range_start", "range_end", "multiplier_value"}
	for _, col := range requiredColumns {
		if _, exists := headerIndexMap[col]; !exists {
			return nil, fmt.Errorf("required column '%s' missing in multiplier value mapping file", col)
		}
	}

	var multiplierValues []multiplierValueRow
	for {
		row, err := reader.Read()
		if err == io.EOF {
			break
		}
		if err != nil {
			return nil, fmt.Errorf("failed to read row: %w", err)
		}

		if len(row) < 3 {
			g.logger.Warn().Msgf("skipping record with insufficient fields: %v", row)
			continue
		}

		multiplierValue := multiplierValueRow{
			rangeStart:      row[headerIndexMap["range_start"]],
			rangeEnd:        row[headerIndexMap["range_end"]],
			multiplierValue: row[headerIndexMap["multiplier_value"]],
		}
		multiplierValues = append(multiplierValues, multiplierValue)
	}

	return multiplierValues, nil
}

func (g *gha) getAccountIdsFromStructureSvc() ([]string, error) {
	marketingChannel := enums.GetMarketingChannelFromString(g.MarketingChannel())
	accountsResp, err := g.structureQueryClient.getAccounts(marketingChannel)
	if err != nil {
		return nil, fmt.Errorf("failed to fetch accounts: %w", err)
	}

	accountIds := make([]string, 0, len(accountsResp))
	for _, account := range accountsResp {
		accountIds = append(accountIds, account.AccountId)
	}

	return accountIds, nil
}

func mapBrandName(brand string) string {
	switch brand {
	case "BEX":
		return "EXPEDIA"
	default:
		return brand
	}
}

// getPartnerPosList returns the appropriate partner POS list based on campaign and brand
func getPartnerPosList(campaignPartnerPos, brandName string) string {
	switch {
	case campaignPartnerPos == "ROA" && brandName == "HCOM":
		return roaHcomPartnerPosList
	case campaignPartnerPos == "ROA" && brandName == "EXPEDIA":
		return roaExpediaPartnerPosList
	case campaignPartnerPos == "ROE" && brandName == "HCOM":
		return roeHcomPartnerPosList
	case campaignPartnerPos == "ROE" && brandName == "EXPEDIA":
		return roeExpediaPartnerPosList
	case campaignPartnerPos == "ROL":
		return rolPartnerPosList
	default:
		return campaignPartnerPos
	}
}

// getAnchorPartnerPos returns the appropriate anchor partner POS based on campaign and brand
func getAnchorPartnerPos(campaignPartnerPos, brandName string) string {
	switch {
	case campaignPartnerPos == "ROA" && brandName == "EXPEDIA":
		return "ROW_APAC"
	case campaignPartnerPos == "ROE" && brandName == "EXPEDIA":
		return "ROW_EMEA"
	case campaignPartnerPos == "ROL" && brandName == "EXPEDIA":
		return "ROW_LATAM"
	case campaignPartnerPos == "ROA" && brandName == "HCOM":
		return "APAC"
	case campaignPartnerPos == "ROE" && brandName == "HCOM":
		return "AE"
	case campaignPartnerPos == "ROL" && brandName == "HCOM":
		return "AR"
	default:
		return campaignPartnerPos
	}
}

// getPlatformList returns the appropriate platform list based on campaign platform, placement type, and brand
func getPlatformList(campaignPlatform, placementType, brandName, campaignPartnerPos string) string {
	switch {
	case campaignPlatform == "MOBILETABLET":
		return "mobile,tablet"
	case campaignPlatform == "MOBILE" && placementType == "PROMOTED":
		return "mobile,tablet"
	case campaignPlatform == "MOBILE" && (brandName == "EXPEDIA" || brandName == "HCOM") && campaignPartnerPos != "US":
		return "mobile,tablet"
	case campaignPlatform == "MOBILE" && brandName != "EXPEDIA" && brandName != "HCOM":
		return "mobile,tablet"
	case campaignPlatform == "ALL":
		return "desktop,mobile,tablet"
	default:
		return strings.ToLower(campaignPlatform)
	}
}

// getAnchorPlatform returns the appropriate anchor platform based on campaign platform
func getAnchorPlatform(campaignPlatform string) string {
	switch campaignPlatform {
	case "MOBILETABLET":
		return "mobile"
	case "ALL":
		return "desktop"
	default:
		return strings.ToLower(campaignPlatform)
	}
}

func (g *gha) parseCampaign(inpCampaign *Campaign, posMapping map[string]string, multiplierMapping []multiplierValueRow) error {
	// Parse BrandName
	brandMatches := brandRegex.FindStringSubmatch(inpCampaign.CampaignName)
	if len(brandMatches) >= 2 {
		inpCampaign.BrandName = mapBrandName(brandMatches[1])
	} else {
		g.logger.Error().Msgf("could not parse brand: %s", inpCampaign.CampaignName)
		return fmt.Errorf("could not parse brand: %s", inpCampaign.CampaignName)
	}

	// Parse CampaignPartnerPos if present
	partnerPosMatches := partnerPosRegex.FindStringSubmatch(inpCampaign.CampaignName)
	if len(partnerPosMatches) >= 2 {
		inpCampaign.CampaignPartnerPos = partnerPosMatches[1]
	} else {
		g.logger.Error().Msgf("could not parse pos for campaign: %s", inpCampaign.CampaignName)
		return fmt.Errorf("could not parse pos for campaign: %s", inpCampaign.CampaignName)
	}

	// Parse CampaignPlatform if present
	platformMatches := platformRegex.FindStringSubmatch(inpCampaign.CampaignName)
	if len(platformMatches) >= 2 {
		inpCampaign.CampaignPlatform = platformMatches[1]
	}

	// Parse ABW information if present
	if strings.Count(inpCampaign.CampaignName, ":") >= 6 {
		abwMatches := abwRegex.FindStringSubmatch(inpCampaign.CampaignName)
		if len(abwMatches) >= 3 {
			inpCampaign.AbwLowerRange = abwMatches[1]
			inpCampaign.AbwUpperRange = abwMatches[2]
		}
	}

	// Parse PlacementType if present
	placementTypeMatches := placementTypeRegex.FindStringSubmatch(inpCampaign.CampaignName)
	if len(placementTypeMatches) >= 2 {
		inpCampaign.PlacementType = placementTypeMatches[1]
	}

	// Set AnchorPartnerPos
	inpCampaign.AnchorPartnerPos = getAnchorPartnerPos(inpCampaign.CampaignPartnerPos, inpCampaign.BrandName)

	// Set PartnerPosList
	inpCampaign.PartnerPosList = getPartnerPosList(inpCampaign.CampaignPartnerPos, inpCampaign.BrandName)

	// Set AnchorPlatform
	inpCampaign.AnchorPlatform = getAnchorPlatform(inpCampaign.CampaignPlatform)

	// Set PlatformList
	inpCampaign.PlatformList = getPlatformList(
		inpCampaign.CampaignPlatform,
		inpCampaign.PlacementType,
		inpCampaign.BrandName,
		inpCampaign.CampaignPartnerPos,
	)

	// Find matching multiplier values
	inpCampaign.AbwMinrangeStart = "0"  // Default value
	inpCampaign.AbwMaxrangeStart = "91" // Default value

	// Only process if we have valid range values
	if inpCampaign.AbwLowerRange != "" && inpCampaign.AbwUpperRange != "" {
		for _, mv := range multiplierMapping {
			if inpCampaign.AbwLowerRange >= mv.rangeStart && inpCampaign.AbwLowerRange <= mv.rangeEnd {
				inpCampaign.AbwMinrangeStart = mv.multiplierValue
			}
			if inpCampaign.AbwUpperRange >= mv.rangeStart && inpCampaign.AbwUpperRange <= mv.rangeEnd {
				inpCampaign.AbwMaxrangeStart = mv.multiplierValue
			}
		}
	}

	// Set PlacementType
	if inpCampaign.PlacementType == "PROMOTED" {
		inpCampaign.PlacementType = "promoted_hotels"
	} else {
		inpCampaign.PlacementType = "core_search"
	}

	// Set SuperRegion
	if superRegion, ok := posMapping[inpCampaign.CampaignPartnerPos]; ok {
		inpCampaign.SuperRegion = superRegion
	} else {
		inpCampaign.SuperRegion = inpCampaign.CampaignPartnerPos
	}

	return nil
}

func (g *gha) runForAccount(
	accountId string,
	posMapping map[string]string,
	multiplierMapping []multiplierValueRow,
	inChan chan<- LokiBidUnit,
	pw *writer.ParquetWriter,
	pwMutex *sync.Mutex,
) error {
	logger := g.logger.With().Str("account_id", accountId).Logger()
	logger.Info().Msg("processing account")

	// Prepare file paths
	accountFile := fmt.Sprintf("%s/%s/eg/campaign/customer_id=%s/dt=latest/file.tsv",
		g.cfg.StructureFilesS3Path, g.MarketingChannel(), accountId)
	accountLocalFile := fmt.Sprintf("%s/inputs/%s.tsv", g.cfg.WorkspaceDir, accountId)

	// Download campaign file
	_, err := g.awsClient.S3DownloadFile(aws.S3Uri(accountFile), accountLocalFile)
	if err != nil {
		return fmt.Errorf("failed to download campaign file: %w", err)
	}

	// Get existing bid units for this account
	labels := map[string]string{"account_id": accountId}
	pbBidUnits, err := g.bidUnitClient.GetBidUnitsByChannelAndLabels(
		enums.GetMarketingChannelFromString(g.MarketingChannel()), labels)
	if err != nil {
		return fmt.Errorf("could not get bid units: %w", err)
	}

	// Create map of existing bid units for efficient lookup
	campaignToBidUnit := make(map[string]bidUnit, len(pbBidUnits))
	for _, pbBidUnit := range pbBidUnits {
		campaignId, ok := pbBidUnit.Labels["campaign_id"]
		if !ok {
			logger.Warn().Str("bid_unit_id", pbBidUnit.Id).Msg("could not find campaign id in labels")
			continue
		}
		campaignToBidUnit[campaignId] = bidUnit{
			id:           pbBidUnit.Id,
			campaignId:   campaignId,
			campaignName: pbBidUnit.Labels["campaign_name"],
			status:       LokiBidUnitStatus(pbBidUnit.Status),
		}
	}

	// Open and process campaign file
	file, err := os.Open(accountLocalFile)
	if err != nil {
		return fmt.Errorf("failed to open campaign file: %w", err)
	}
	defer file.Close()

	reader := csv.NewReader(bufio.NewReader(file))
	reader.Comma = '\t'

	// Read and validate headers
	headers, err := reader.Read()
	if err != nil {
		return fmt.Errorf("failed to read headers: %w", err)
	}

	headerIndexMap := make(map[string]int)
	for i, field := range headers {
		headerIndexMap[field] = i
	}

	mandatoryFields := []string{"customer.id", "customer.descriptive_name", "campaign.id", "campaign.name", "campaign.status"}
	for _, field := range mandatoryFields {
		if _, exists := headerIndexMap[field]; !exists {
			return fmt.Errorf("mandatory field missing in header: %s", field)
		}
	}

	// Process campaigns
	processedCampaigns := make(map[string]bool)

	for {
		row, err := reader.Read()
		if err == io.EOF {
			break
		}
		if err != nil {
			logger.Warn().Err(err).Msg("failed to read row, skipping")
			continue
		}

		// Create campaign object
		campaignRow := &Campaign{
			AccountId:    row[headerIndexMap["customer.id"]],
			AccountName:  row[headerIndexMap["customer.descriptive_name"]],
			CampaignId:   row[headerIndexMap["campaign.id"]],
			CampaignName: row[headerIndexMap["campaign.name"]],
			Status:       ENABLED,
		}

		status, _ := strconv.Atoi(row[headerIndexMap["campaign.status"]])
		if status != 2 {
			campaignRow.Status = DISABLED
		}

		// Parse campaign data
		err = g.parseCampaign(campaignRow, posMapping, multiplierMapping)
		if err != nil {
			logger.Warn().
				Err(err).
				Str("campaign_id", campaignRow.CampaignId).
				Msg("failed to parse campaign, skipping")
			processedCampaigns[campaignRow.CampaignId] = true
			continue
		}

		// Write to parquet file (thread-safe)
		pwMutex.Lock()
		err = pw.Write(*campaignRow)
		pwMutex.Unlock()

		if err != nil {
			logger.Warn().
				Err(err).
				Str("campaign_id", campaignRow.CampaignId).
				Msg("failed to write campaign to parquet file")
		}

		// Mark as processed
		processedCampaigns[campaignRow.CampaignId] = true

		// Generate bid unit labels
		bidUnitLabels := getLabelsFromCampaign(campaignRow)

		// Check if bid unit exists and needs updating
		if bu, ok := campaignToBidUnit[campaignRow.CampaignId]; ok {
			// pops from the map
			delete(campaignToBidUnit, campaignRow.CampaignId)
			if bu.campaignName == campaignRow.CampaignName && bu.status == campaignRow.Status {
				continue
			}
			inChan <- LokiBidUnit{
				Id:               bu.id,
				Name:             fmt.Sprintf("%s-%s", campaignRow.AccountName, campaignRow.CampaignName),
				MarketingChannel: g.MarketingChannel(),
				Labels:           bidUnitLabels,
				Status:           campaignRow.Status,
			}
		} else {
			// New campaign, create bid unit
			inChan <- LokiBidUnit{
				Name:             fmt.Sprintf("%s-%s", campaignRow.AccountName, campaignRow.CampaignName),
				MarketingChannel: g.MarketingChannel(),
				Labels:           bidUnitLabels,
				Status:           campaignRow.Status,
			}
		}
	}

	// Process any remaining bid units that need to be disabled
	for _, bu := range campaignToBidUnit {
		inChan <- LokiBidUnit{
			Id:     bu.id,
			Status: DISABLED,
		}
	}

	return nil
}

func getLabelsFromCampaign(campaign *Campaign) map[string]string {
	return map[string]string{
		"account_name":        campaign.AccountName,
		"account_id":          campaign.AccountId,
		"campaign_name":       campaign.CampaignName,
		"campaign_id":         campaign.CampaignId,
		"anchor_partner_pos":  campaign.AnchorPartnerPos,
		"partner_pos_list":    campaign.PartnerPosList,
		"anchor_platform":     campaign.AnchorPlatform,
		"platform_list":       campaign.PlatformList,
		"abw_min_range_start": campaign.AbwMinrangeStart,
		"abw_max_range_start": campaign.AbwMaxrangeStart,
		"placement_type":      campaign.PlacementType,
		"super_region":        campaign.SuperRegion,
		"brand":               campaign.BrandName,
	}
}
