package job

import (
	"context"
	"fmt"

	"github.com/rs/zerolog"
	enumsv1 "github.expedia.biz/gmo-performance-marketing/pb-go/pkg/enums/v1"
	pb "github.expedia.biz/gmo-performance-marketing/pb-go/pkg/structure/query/v1"
	"github.expedia.biz/gmo-performance-marketing/sem-lib-go/pkg/grpc"
)

type structureQueryClient interface {
	getAccounts(channel enumsv1.MarketingChannel) ([]*pb.Account, error)
}

type structureQueryImpl struct {
	client pb.AccountServiceClient
	l      zerolog.Logger
}

func newStructureQueryClient(svcAddress string, l zerolog.Logger) (*structureQueryImpl, error) {
	conn, err := grpc.DialWithRetry(svcAddress, []string{"loki.v1.StructureQueryService"})
	if err != nil {
		return nil, fmt.Errorf("grpc dial loki structure query service failed: %v", err)
	}
	return &structureQueryImpl{client: pb.NewAccountServiceClient(conn), l: l}, nil
}

func (sq *structureQueryImpl) getAccounts(channel enumsv1.MarketingChannel) ([]*pb.Account, error) {
	resp, err := sq.client.GetAccounts(context.Background(), &pb.GetAccountsRequest{
		MarketingChannel:       channel,
		Client:                 "eg",
		IncludeManagerAccounts: false,
	})
	if err != nil {
		return nil, fmt.Errorf("failed to fetch accounts: %w", err)
	}

	return resp.Accounts, nil
}
