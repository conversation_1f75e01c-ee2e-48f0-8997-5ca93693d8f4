package main

import (
	"context"
	"flag"
	"fmt"
	"os"
	"strings"
	"sync"
	"time"

	"github.expedia.biz/gmo-performance-marketing/loki/cmd/bidunit/job"
	"github.expedia.biz/gmo-performance-marketing/loki/internal/client"
	enumsv1 "github.expedia.biz/gmo-performance-marketing/pb-go/pkg/enums/v1"
	pb "github.expedia.biz/gmo-performance-marketing/pb-go/pkg/loki/v1"
	notificationv1 "github.expedia.biz/gmo-performance-marketing/pb-go/pkg/notification/v1"
	"github.expedia.biz/gmo-performance-marketing/sem-lib-go/pkg/aws"
	"github.expedia.biz/gmo-performance-marketing/sem-lib-go/pkg/enums"
	"github.expedia.biz/gmo-performance-marketing/sem-lib-go/pkg/logger"
	"google.golang.org/grpc/metadata"
)

func main() {
	l := logger.Get().With().Str("service", "loki").Str("component", "bidunit").Logger()

	// Parse command line arguments
	marketingChannelInp := flag.String("marketingChannel", "", "marketing channel for bid unit run (e.g., google-hotel-ads)")
	workflowId := flag.String("workflowId", "", "workflow ID for tracking and notifications")
	flag.Parse()

	// Validate marketing channel
	if *marketingChannelInp == "" {
		l.Fatal().Msg("marketing channel is required, use -marketingChannel flag")
	}

	// Check if the marketing channel is supported
	if !job.IsChannelSupported(*marketingChannelInp) {
		l.Fatal().Msgf("unsupported marketing channel: %s (supported channels: %s)",
			*marketingChannelInp, strings.Join(job.SupportedChannels, ", "))
	}

	l.Info().Msgf("Starting bid unit job for marketing channel: %s", *marketingChannelInp)

	// Convert string to enum value
	pbMarketingChannel := enums.GetMarketingChannelFromString(*marketingChannelInp)
	if pbMarketingChannel == enumsv1.MarketingChannel_MARKETING_CHANNEL_UNSPECIFIED {
		l.Fatal().Msgf("invalid marketing channel enum value for: %s", *marketingChannelInp)
	}
	cfg, err := job.GetConfig()
	if err != nil {
		l.Fatal().Err(err).Msg("failed to load bid unit job config")
	}
	l.Info().Msgf("configuration loaded: %v", cfg)

	err = os.MkdirAll(fmt.Sprintf("%s/inputs", cfg.WorkspaceDir), os.ModePerm)
	if err != nil {
		l.Fatal().Err(err).Msg("failed to create inputs directory")
	}

	err = os.MkdirAll(fmt.Sprintf("%s/outputs", cfg.WorkspaceDir), os.ModePerm)
	if err != nil {
		l.Fatal().Err(err).Msg("failed to create outputs directory")
	}

	awsClient, err := aws.NewClient(cfg.AwsRegion)
	if err != nil {
		l.Fatal().Err(err).Msg("failed to create aws client")
	}

	bidUnitClient, err := client.NewBidUnitClient(l, cfg.LokiSvcAddr)
	if err != nil {
		l.Fatal().Err(err).Msg("failed to create bid unit client")
	}

	notificationClient, err := client.NewNotificationClient(l, cfg.OdinSvcAddr)
	if err != nil {
		l.Fatal().Err(err).Msg("failed to create notification client")
	}

	bidUnitJob, err := job.GetBidUnitJob(*marketingChannelInp, cfg, l, awsClient, bidUnitClient)
	if err != nil {
		l.Fatal().Err(err).Msg("failed to load bid unit job")
	}
	inChan := make(chan job.LokiBidUnit)

	type bidUnitStats struct {
		disabledBidUnits int32
		newBidUnits      int32
		updateBidUnits   int32
	}
	var wg sync.WaitGroup
	stats := &bidUnitStats{}

	// Create a worker pool for processing bid units
	numWorkers := cfg.MaxWorkers                   // Use configured value
	bidUnitChan := make(chan job.LokiBidUnit, 100) // Buffered channel for better performance

	// Create context with timeout and metadata
	header := metadata.New(map[string]string{"user": "loki"})
	ctx, cancel := context.WithTimeout(context.Background(), time.Duration(cfg.RequestTimeout)*time.Minute)
	ctx = metadata.NewOutgoingContext(ctx, header)
	defer cancel()

	// Start worker pool
	for i := 0; i < numWorkers; i++ {
		wg.Add(1)
		go func() {
			defer wg.Done()
			for bu := range bidUnitChan {
				if bu.Id != "" {
					err := bidUnitClient.UpdateBidUnitStatus(ctx, bu.Id, bu.Name, bu.Labels, pb.BidUnitStatus(bu.Status))
					if err != nil {
						l.Error().Err(err).Str("bid_unit_id", bu.Id).Msg("failed to update bid unit status")
					} else {
						if bu.Status == job.DISABLED {
							stats.disabledBidUnits++
						} else {
							stats.updateBidUnits++
						}
					}
					continue
				}

				err := bidUnitClient.CreateBidUnit(ctx, bu.Name, pbMarketingChannel, bu.Labels, pb.BidUnitStatus(bu.Status))
				if err != nil {
					l.Error().Err(err).
						Str("bid_unit_name", bu.Name).
						Str("marketing_channel", bu.MarketingChannel).
						Msg("failed to create bid unit")
				} else {
					stats.newBidUnits++
				}
			}
		}()
	}

	// Forward bid units from inChan to bidUnitChan
	go func() {
		for bu := range inChan {
			bidUnitChan <- bu
		}
		close(bidUnitChan)
	}()

	err = bidUnitJob.GenerateBidUnits(inChan)
	close(inChan)
	wg.Wait()

	if strings.ToLower(cfg.Env) != "dev" {
		latestOutputsS3Path := aws.S3Uri(fmt.Sprintf("%s/channel=%s/dt=latest", cfg.BidUnitS3Path, *marketingChannelInp))
		dateOutputsS3Path := aws.S3Uri(fmt.Sprintf("%s/channel=%s/dt=%s", cfg.BidUnitS3Path, *marketingChannelInp, time.Now().Format("2006-01-02")))

		_, err = awsClient.S3UploadDir(fmt.Sprintf("%s/outputs/", cfg.WorkspaceDir), latestOutputsS3Path)
		if err != nil {
			l.Error().Err(err).Msg("failed to upload outputs directory")
		}

		_, err = awsClient.S3UploadDir(fmt.Sprintf("%s/outputs/", cfg.WorkspaceDir), dateOutputsS3Path)
		if err != nil {
			l.Error().Err(err).Msg("failed to upload outputs directory")
		}

		trackingUrl := fmt.Sprintf("%s/%s", cfg.TrackingUrl, *workflowId)
		fields := []*notificationv1.Field{
			{Key: "Channel", Value: *marketingChannelInp},
			{Key: "Tracking Url", Value: trackingUrl},
			{Key: "Disabled Bid Units", Value: fmt.Sprintf("%d", stats.disabledBidUnits)},
			{Key: "New Bid Units", Value: fmt.Sprintf("%d", stats.newBidUnits)},
			{Key: "Update Bid Units", Value: fmt.Sprintf("%d", stats.updateBidUnits)},
		}
		if err != nil {
			fields = append(fields, &notificationv1.Field{Key: "Error", Value: err.Error(), Color: "red"})
		}
		notification := &notificationv1.Notification{
			Title: "Bid Unit Job",
			Sections: []*notificationv1.Section{
				{Section: &notificationv1.Section_Fields{
					Fields: &notificationv1.Fields{
						Fields: fields}}},
			},
		}

		notificationErr := notificationClient.SendSlackNotification(notification, cfg.SlackChannel)
		if notificationErr != nil {
			l.Error().Err(notificationErr).Msgf("sending slack notification")
		}
	}

	if err != nil {
		l.Fatal().Err(err).Msg("failed to process bid unit job")
	}
}
