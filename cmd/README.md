# Running Jobs Locally

This document provides instructions on how to run the various jobs in this project locally for development and testing purposes.

There are two primary ways to run the jobs:

1.  **Using the command line after building the executables.**
2.  **Directly from your Go IDE.**

## Building and Running from the Command Line

This is the recommended approach for simple test runs.

### 1. Build the Executables

The project includes a `Makefile` that simplifies the build process. To build all the job executables, run the following command from the root of the project:

```bash
make build
```

This command executes the `scripts/build.sh` script, which compiles the `main.go` file for each job and places the resulting executables in the `bin/` directory.

The following executables will be created:

*   `bin/bidassembly`
*   `bin/entity`
*   `bin/pipeline-run-poller`
*   `bin/bidunit`
*   `bin/run-handler`

### 2. Run the Job

Once the executables are built, you can run any of them from the command line. You will need to provide the required command-line flags for the specific job you are running.

#### Example: Running the Bid Assembly Job

The Bid Assembly job (`bidassembly`) requires a `runId` and `workflowId` to be passed as flags.

```bash
./bin/bidassembly -runId "your-run-id" -workflowId "your-workflow-id"
```

You can find the required flags for each job by inspecting its `main.go` file under the corresponding `cmd/` subdirectory.

## Running from a Go IDE

For debugging purposes, running the jobs directly from a Go IDE (like GoLand or VS Code with the Go extension) is often more convenient.

1.  **Open the Project**: Open the project root directory in your IDE.
2.  **Find the `main` function**: Navigate to the `main.go` file for the job you want to run (e.g., `cmd/assembler/bidassembly/main.go`).
3.  **Create a Run Configuration**: Create a new run/debug configuration for that `main.go` file.
4.  **Set Program Arguments**: In the run configuration settings, find the option for "Program arguments" or "Flags" and add the required command-line flags. For the Bid Assembly job, you would add:
    ```
    -runId "your-run-id" -workflowId "your-workflow-id"
    ```
5.  **Set Environment Variables**: Ensure you have all the necessary environment variables set as described in the respective job's documentation (e.g., `internal/assembler/bidassembly/README.md`). You can usually set these within the run configuration as well.
6.  **Run or Debug**: You can now run or debug the job directly from your IDE.
