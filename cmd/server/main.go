package main

import (
	"github.expedia.biz/gmo-performance-marketing/loki/internal"
	"github.expedia.biz/gmo-performance-marketing/sem-lib-go/pkg/aws"
	loggerlib "github.expedia.biz/gmo-performance-marketing/sem-lib-go/pkg/logger"
	"gorm.io/driver/postgres"
)

func main() {
	logger := loggerlib.Get().With().Str("workload", "loki.server").Logger()

	cfg, err := internal.NewServerConfig()
	if err != nil {
		logger.Fatal().Err(err).Msg("failed to init server config")
	}

	orm, err := internal.Orm(postgres.Open(cfg.DbDsn()))
	if err != nil {
		logger.Fatal().Err(err).Msg("failed to create orm")
	}
	awsClient, err := aws.NewClientWithLogger(cfg.AWSRegion, logger)
	if err != nil {
		logger.Fatal().Err(err).Msg("failed to initialize AWS client")
	}
	server := internal.NewServer(cfg.Port, cfg.MetricsPort, cfg.DebugUiPort, cfg.EnableReflection, orm, logger, awsClient)
	err = server.Serve()
	if err != nil {
		logger.Fatal().Err(err).Msg("failed to start the gRPC server")
	}
}
